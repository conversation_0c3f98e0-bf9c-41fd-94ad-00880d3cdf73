package org.jeecg.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.SysMenu;
import org.jeecg.modules.system.mapper.SysMenuMapper;
import org.jeecg.modules.system.service.ISysMenuService;
import org.jeecg.modules.system.vo.MenuTreeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单表 服务实现类
 */
@Slf4j
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements ISysMenuService {

    @Autowired
    private SysMenuMapper sysMenuMapper;

    @Override
    public List<MenuTreeVO> getAllMenus() {
        // 获取所有菜单
        List<SysMenu> allMenus = this.list(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getDelFlag, 0)
                .orderByAsc(SysMenu::getOrderNum));

        // 构建菜单树
        return buildMenuTree(allMenus);
    }

    @Override
    public List<String> getVisibleMenus() {
        return sysMenuMapper.getVisibleMenuKeys();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMenuVisibility(List<String> menuKeys, String username) {
        // 先将所有菜单设置为不可见
        sysMenuMapper.setAllMenusInvisible(username);

        // 如果有可见菜单，则将其设置为可见
        if (menuKeys != null && !menuKeys.isEmpty()) {
            // 获取所有菜单
            List<SysMenu> allMenus = this.list(new LambdaQueryWrapper<SysMenu>()
                    .eq(SysMenu::getDelFlag, 0));

            // 构建菜单键到菜单的映射
            Map<String, SysMenu> menuKeyMap = allMenus.stream()
                    .collect(Collectors.toMap(SysMenu::getMenuKey, menu -> menu, (a, b) -> a));

            // 构建ID到菜单的映射
            Map<String, SysMenu> idMap = allMenus.stream()
                    .collect(Collectors.toMap(SysMenu::getId, menu -> menu, (a, b) -> a));

            // 确保父菜单也可见
            Set<String> visibleMenuKeys = new HashSet<>(menuKeys);
            for (String menuKey : menuKeys) {
                SysMenu menu = menuKeyMap.get(menuKey);
                if (menu != null && menu.getParentId() != null) {
                    // 递归添加所有父菜单
                    addParentMenus(menu.getParentId(), idMap, visibleMenuKeys);
                }
            }

            // 将可见菜单键列表转换为List
            List<String> finalVisibleMenuKeys = new ArrayList<>(visibleMenuKeys);

            return sysMenuMapper.setMenusVisible(finalVisibleMenuKeys, username) > 0;
        }

        return true;
    }

    /**
     * 递归添加父菜单
     *
     * @param parentId 父菜单ID
     * @param idMap ID到菜单的映射
     * @param visibleMenuKeys 可见菜单键集合
     */
    private void addParentMenus(String parentId, Map<String, SysMenu> idMap, Set<String> visibleMenuKeys) {
        // 根据ID查找菜单
        SysMenu parentMenu = idMap.get(parentId);

        if (parentMenu != null) {
            // 添加父菜单键
            visibleMenuKeys.add(parentMenu.getMenuKey());

            // 如果父菜单还有父菜单，递归添加
            if (parentMenu.getParentId() != null) {
                addParentMenus(parentMenu.getParentId(), idMap, visibleMenuKeys);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initMenuData(String username) {
        // 定义菜单数据
        List<Map<String, Object>> menuData = new ArrayList<>();

        // 首页
        menuData.add(createMenuData("guide", "首页", null, 1, "/dashboard/analysis", "dashboard/Analysis", true, true, true, "home", "首页"));

        // 企业管理
        menuData.add(createMenuData("corp", "企业管理", null, 2, "/corp", null, true, false, true, "bank", "企业管理"));
        menuData.add(createMenuData("pdLedgerList", "车险台账", "corp", 1, "/corp/pdLedgerList", "Corp/PdLedgerList", true, true, true, "car", "车险台账"));
        menuData.add(createMenuData("pdInsuranceLedgerList", "财险台账", "corp", 2, "/corp/pdInsuranceLedgerList", "Corp/PdInsuranceLedgerList", true, true, true, "insurance", "财险台账"));
        menuData.add(createMenuData("pdAddedLedgerList", "增值服务台账", "corp", 3, "/corp/pdAddedLedgerList", "Corp/PdAddedLedgerList", true, true, true, "plus-circle", "增值服务台账"));

        // 信息管理
        menuData.add(createMenuData("info", "信息管理", null, 3, "/info", null, true, false, true, "info-circle", "信息管理"));
        menuData.add(createMenuData("pdChatSourceList", "信息源列表", "info", 1, "/info/pdChatSourceList", "Info/PdChatSourceList", true, true, true, "unordered-list", "信息源列表"));
        menuData.add(createMenuData("pdChatSourceDetList", "信息源详情列表", "info", 2, "/info/pdChatSourceDetList", "Info/PdChatSourceDetList", true, true, true, "ordered-list", "信息源详情列表"));

        // 系统管理
        menuData.add(createMenuData("system", "系统管理", null, 4, "/system", null, true, false, true, "setting", "系统管理"));
        menuData.add(createMenuData("menuConfig", "菜单配置", "system", 1, "/system/menuConfig", "System/MenuConfig", true, true, true, "menu", "菜单配置"));
        menuData.add(createMenuData("clickPreProgress", "点击数预生成进度", "system", 2, "/system/clickPreProgress", "System/ClickPreProgress", true, true, true, "bar-chart", "点击数预生成进度监控"));

        // 保存菜单数据
        for (Map<String, Object> menu : menuData) {
            // 检查菜单是否已存在
            SysMenu existMenu = sysMenuMapper.getByMenuKey((String) menu.get("menuKey"));
            if (existMenu != null) {
                continue;
            }

            // 创建菜单
            SysMenu sysMenu = new SysMenu();
            sysMenu.setMenuKey((String) menu.get("menuKey"));
            sysMenu.setMenuName((String) menu.get("menuName"));
            sysMenu.setParentId((String) menu.get("parentId"));
            sysMenu.setOrderNum((Integer) menu.get("orderNum"));
            sysMenu.setPath((String) menu.get("path"));
            sysMenu.setComponent((String) menu.get("component"));
            sysMenu.setIsRoute((Boolean) menu.get("isRoute"));
            sysMenu.setIsLeaf((Boolean) menu.get("isLeaf"));
            sysMenu.setIsVisible((Boolean) menu.get("isVisible"));
            sysMenu.setIcon((String) menu.get("icon"));
            sysMenu.setDescription((String) menu.get("description"));
            sysMenu.setCreateBy(username);
            sysMenu.setCreateTime(new Date());
            sysMenu.setDelFlag(false);

            this.save(sysMenu);
        }

        return true;
    }

    /**
     * 构建菜单树
     *
     * @param allMenus 所有菜单
     * @return 菜单树
     */
    private List<MenuTreeVO> buildMenuTree(List<SysMenu> allMenus) {
        // 创建 menuKey 到 ID 的映射
        Map<String, String> menuKeyToIdMap = new HashMap<>();
        // 创建 ID 到 menuKey 的映射
        Map<String, String> idToMenuKeyMap = new HashMap<>();

        // 填充映射
        for (SysMenu menu : allMenus) {
            menuKeyToIdMap.put(menu.getMenuKey(), menu.getId());
            idToMenuKeyMap.put(menu.getId(), menu.getMenuKey());
        }

        // 按父ID分组
        Map<String, List<SysMenu>> parentIdMap = allMenus.stream()
                .collect(Collectors.groupingBy(menu -> menu.getParentId() == null ? "" : menu.getParentId()));

        // 构建根节点
        List<MenuTreeVO> rootNodes = parentIdMap.getOrDefault("", new ArrayList<>()).stream()
                .map(menu -> convertToTreeNode(menu, menuKeyToIdMap, idToMenuKeyMap))
                .collect(Collectors.toList());

        // 递归构建子节点
        for (MenuTreeVO node : rootNodes) {
            buildChildren(node, parentIdMap, menuKeyToIdMap, idToMenuKeyMap);
        }

        return rootNodes;
    }

    /**
     * 递归构建子节点
     *
     * @param parentNode 父节点
     * @param parentIdMap 按父ID分组的菜单
     * @param menuKeyToIdMap menuKey 到 ID 的映射
     * @param idToMenuKeyMap ID 到 menuKey 的映射
     */
    private void buildChildren(MenuTreeVO parentNode, Map<String, List<SysMenu>> parentIdMap,
                              Map<String, String> menuKeyToIdMap, Map<String, String> idToMenuKeyMap) {
        // 使用父节点的 ID 查找子节点
        String parentId = parentNode.getId();
        if (parentId == null) {
            return;
        }

        List<SysMenu> children = parentIdMap.getOrDefault(parentId, new ArrayList<>());
        if (!children.isEmpty()) {
            List<MenuTreeVO> childNodes = children.stream()
                    .map(menu -> convertToTreeNode(menu, menuKeyToIdMap, idToMenuKeyMap))
                    .collect(Collectors.toList());
            parentNode.setChildren(childNodes);

            // 递归构建子节点的子节点
            for (MenuTreeVO childNode : childNodes) {
                buildChildren(childNode, parentIdMap, menuKeyToIdMap, idToMenuKeyMap);
            }
        }
    }

    /**
     * 将菜单转换为树节点
     *
     * @param menu 菜单
     * @param menuKeyToIdMap menuKey 到 ID 的映射
     * @param idToMenuKeyMap ID 到 menuKey 的映射
     * @return 树节点
     */
    private MenuTreeVO convertToTreeNode(SysMenu menu, Map<String, String> menuKeyToIdMap, Map<String, String> idToMenuKeyMap) {
        MenuTreeVO node = new MenuTreeVO();
        node.setKey(menu.getMenuKey());
        node.setTitle(menu.getMenuName());
        // 存储菜单 ID，用于查找子节点
        node.setId(menu.getId());
        // 存储父菜单 ID
        node.setParentId(menu.getParentId());
        return node;
    }

    /**
     * 创建菜单数据
     */
    private Map<String, Object> createMenuData(String menuKey, String menuName, String parentId, Integer orderNum,
                                              String path, String component, Boolean isRoute, Boolean isLeaf,
                                              Boolean isVisible, String icon, String description) {
        Map<String, Object> menu = new HashMap<>();
        menu.put("menuKey", menuKey);
        menu.put("menuName", menuName);
        menu.put("parentId", parentId);
        menu.put("orderNum", orderNum);
        menu.put("path", path);
        menu.put("component", component);
        menu.put("isRoute", isRoute);
        menu.put("isLeaf", isLeaf);
        menu.put("isVisible", isVisible);
        menu.put("icon", icon);
        menu.put("description", description);
        return menu;
    }
}
