<template>
  <div class="md:flex">
    <template v-for="(item, index) in dataList" :key="item.title">
      <ChartCard
        :loading="loading"
        :title="item.title"
        :total="getTotal(item.prop, index)"
        class="md:w-1/4 w-full !md:mt-0 !mt-4"
        :class="[index + 1 < 4 && '!md:mr-4']"
      >
        <template #titleAction>
          <!-- 刷新按钮 - 只在今日点击卡片显示 -->
          <a-button
            v-if="item.showRefresh || index === 0"
            type="text"
            size="small"
            @click="handleRefresh(item, index)"
            :loading="refreshLoading[index]"
            title="刷新数据"
            class="refresh-btn"
          >
            <ReloadOutlined />
          </a-button>
        </template>
        <template #action>
          <a-tooltip title="">
            <Icon :icon="item.icon" :size="20" />
          </a-tooltip>
        </template>
        <div v-if="type === 'chart'">
          <SingleLine v-if="index === 1" :option="option" :chartData="chartData" :seriesColor="seriesColor" height="50px"></SingleLine>

          <Bar v-if="index === 2" :option="option" :chartData="chartData" :seriesColor="seriesColor" height="50px"></Bar>

          <Progress v-if="index === 3" :percent="totalData.percent" :show-info="false"></Progress>
        </div>
        <div v-else>
          <SingleLine :seriesColor="seriesColor" v-if="index === 0" :option="option" :chartData="chartData" height="50px"></SingleLine>

          <SingleLine :seriesColor="seriesColor" v-if="index === 1" :option="option" :chartData="chartData" height="50px"></SingleLine>

          <Bar :seriesColor="seriesColor" v-if="index === 2" :option="option" :chartData="chartData" height="50px"></Bar>

          <Progress v-if="index === 3" :percent="totalData.percent" :show-info="false"></Progress>

        </div>
        <template #footer v-if="type === 'chart'">
          <!-- 今日点击卡片显示更新时间 -->
          <div v-if="index === 0" class="text-xs text-gray-500">
            <span v-if="item.updateTime">更新时间: {{ item.updateTime }}</span>
            <span v-else>暂无更新时间</span>
          </div>
          <!-- 其他卡片显示原有内容 -->
          <div v-else-if="index !== 3">
            <span>{{ item.footer }}<span>{{ item.value }}</span></span>
          </div>
          <!-- 运营效果卡片显示趋势 -->
          <div v-if="index === 3">
            <Trend term="周同比" :percentage="trendsData.weekly" />
            <Trend term="日同比" :percentage="trendsData.daily" :type="false" />
          </div>
        </template>
        <template #footer v-else>
          <span
            >{{ item.footer }}<span>{{ item.value }}</span></span
          >
        </template>
      </ChartCard>
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import { ReloadOutlined } from '@ant-design/icons-vue';
  import { Icon } from '/@/components/Icon';
  import { Progress, Button } from 'ant-design-vue';
  import ChartCard from '/@/components/chart/ChartCard.vue';
  import Trend from '/@/components/chart/Trend.vue';
  import Bar from '/@/components/chart/Bar.vue';
  import SingleLine from '/@/components/chart/SingleLine.vue';
  import { chartCardList, bdcCardList } from '../data';
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  import { queryByCode, getTodayClickCount, getOperationTrends } from '../api';
  import { formatLargeNumber, formatNumberWithCommas } from '/@/utils/numberFormat';

  const { getThemeColor } = useRootSetting();
  const props = defineProps({
    loading: {
      type: Boolean,
    },
    type: {
      type: String,
      default: 'chart',
    },
  });
const option = ref({ xAxis: { show: false, boundaryGap: false }, yAxis: { show: false, boundaryGap: false } });

const totalData = ref({})
  const chartData = ref([])
  const trendsData = ref({ weekly: 12, daily: 11 }) // 趋势数据
  const seriesColor = computed(() => {
    return getThemeColor.value;
  })
  // 创建响应式的数据列表
  const dataList = ref([...chartCardList]);

  // 定时器变量
  let refreshTimer: NodeJS.Timeout | null = null;

  // 刷新加载状态
  const refreshLoading = ref([false, false, false, false]);

  function getTotal(prop, index) {
    const value = totalData.value[prop];

    // 根据不同的卡片显示不同的格式
    switch (index) {
      case 1: // 满意度
        return `${value || 0}%`;
      case 2: // 用户停留时长
        return `${value || 0}秒`;
      case 3: // 运营效果
        return `${value || 0}%`;
      default: // 今日点击
        return value || 0;
    }
  }

  async function getSalesInfo() {
    try {
      // 并行调用三个接口
      const [configData, todayClickData, trendsResult] = await Promise.all([
        queryByCode({code: 'home_code'}),
        getTodayClickCount(),
        getOperationTrends()
      ]);

      const { clickSum, companySum, integerList, percent, activeProduct } = configData;

      // 使用今日点击数接口的数据替换clickTotal
      totalData.value = {
        clickSum,
        clickTotal: todayClickData?.todayClickCount || 0, // 使用今日点击数
        companySum,
        activeProduct, // 用户停留时长
        percent: percent ? Number(percent) : percent
      }

      // 更新趋势数据
      if (trendsResult && trendsResult.weekly !== undefined && trendsResult.daily !== undefined) {
        trendsData.value = {
          weekly: trendsResult.weekly,
          daily: trendsResult.daily
        };
      }

      // 设置初始更新时间
      updateCardTime(0);

      // 柱状图数据
      const barData = integerList.map((ele) => {
        return {
          name: '',
          value: ele
        }
      })
      chartData.value = barData
    } catch (error) {
      console.error('获取销售信息失败:', error);
      // 设置默认值
      totalData.value = {
        clickSum: 0,
        clickTotal: 0,
        companySum: 0,
        activeProduct: 0,
        percent: 0
      }
    }
  }

  // 只刷新今日点击数的函数
  async function refreshTodayClickCount() {
    try {
      const todayClickData = await getTodayClickCount();
      if (todayClickData?.todayClickCount !== undefined) {
        totalData.value.clickTotal = todayClickData.todayClickCount;
        // 更新时间
        updateCardTime(0);
      }
    } catch (error) {
      console.error('刷新今日点击数失败:', error);
    }
  }

  // 手动刷新处理函数
  async function handleRefresh(item: any, index: number) {
    if (item.prop === 'clickTotal') {
      refreshLoading.value[index] = true;
      try {
        await refreshTodayClickCount();
      } finally {
        refreshLoading.value[index] = false;
      }
    }
  }

  // 更新卡片时间
  function updateCardTime(index: number) {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    // 更新对应卡片的时间
    if (dataList.value[index]) {
      // 创建新的对象来触发响应式更新
      dataList.value[index] = {
        ...dataList.value[index],
        updateTime: timeString
      };
    }
  }

  // 刷新间隔配置
  const refreshConfig = {
    minInterval: 60, // 最小间隔（秒）
    maxInterval: 66, // 最大间隔（秒）
  };

  // 生成随机刷新间隔
  const getRandomRefreshInterval = () => {
    const min = refreshConfig.minInterval * 1000; // 转换为毫秒
    const max = refreshConfig.maxInterval * 1000; // 转换为毫秒
    const randomInterval = min + Math.floor(Math.random() * (max - min));
    return randomInterval;
  }

  // 启动定时刷新
  const startRefreshTimer = () => {
    const scheduleNextRefresh = () => {
      const interval = getRandomRefreshInterval();
      console.log(`下次刷新将在 ${interval / 1000} 秒后执行`);

      refreshTimer = setTimeout(() => {
        refreshTodayClickCount();
        // 刷新完成后，安排下一次刷新
        scheduleNextRefresh();
      }, interval);
    };

    // 开始第一次调度
    scheduleNextRefresh();
  }

  // 清理定时器
  const clearRefreshTimer = () => {
    if (refreshTimer) {
      clearTimeout(refreshTimer);
      refreshTimer = null;
      console.log('定时刷新已停止');
    }
  }

  onMounted(() => {
    getSalesInfo();
    startRefreshTimer(); // 启动定时刷新
  });

  onUnmounted(() => {
    clearRefreshTimer(); // 清理定时器
  });
</script>

<style scoped>
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-gray-500 {
  color: #6b7280;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.refresh-btn {
  padding: 0 4px !important;
  height: 20px !important;
  min-width: auto !important;
  font-size: 12px !important;
  line-height: 1 !important;
  border: none !important;
  background: transparent !important;
  color: rgba(0, 0, 0, 0.45) !important;
  transition: color 0.3s ease !important;
}

.refresh-btn:hover {
  color: #1890ff !important;
  background: rgba(24, 144, 255, 0.1) !important;
}

.refresh-btn:focus {
  color: #1890ff !important;
}
</style>
