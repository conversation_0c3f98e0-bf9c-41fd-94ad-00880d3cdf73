package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.info.util.ChinaPlateNumberGenerator;
import org.jeecg.modules.info.util.LicensePlateGenerator;
import org.jeecg.config.util.LicensePlateToProvince;
import org.jeecg.modules.corp.dto.*;
import org.jeecg.modules.corp.entity.*;
import org.jeecg.modules.corp.mapper.ClickAutoPreDao;
import org.jeecg.modules.corp.service.*;
import org.jeecg.modules.corp.util.*;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.service.IClickReportService;
import org.jeecg.modules.info.dto.LedgerChatDto;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.info.service.IPdCarInfoService;
import org.jeecg.modules.info.service.IPdCasualtyInfoService;
import org.jeecg.modules.info.service.IPdChatSourceService;
import org.jeecg.modules.info.service.IPdSceneService;
import org.jeecg.modules.wechat.dto.config.DeployConfigDTO;
import org.jeecg.modules.wechat.entity.PdIntegrated;
import org.jeecg.modules.wechat.service.IPdIntegratedService;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.jeecg.modules.corp.service.IPdCarInfoRelService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.service.ISysTenantService;
import org.jeecg.modules.wechat.entity.EaRegion;
import org.jeecg.modules.wechat.service.IEaRegionService;
import org.jeecg.modules.corp.vo.CityInfoVO;
import org.jeecg.modules.corp.service.ISysCityPlatePrefixService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Service
@Slf4j
public class ClickAutoPreServiceImpl extends ServiceImpl<ClickAutoPreDao, ClickAutoPre> implements IClickAutoPreService {
    @Autowired
    private ISysDeployConfigService deployConfigService;

    @Autowired
    private IClickReportService clickReportService;

    @Autowired
    private ObjectMapper objectMapper;

    Random random = new Random();



    @Autowired
    private IPdInsuranceLedgerService iPdInsuranceLedgerService;

    @Autowired
    private IPdIntegratedService pdIntegratedService;

    @Autowired
    private IPdAddedLedgerService iPdAddedLedgerService;

    @Autowired
    private IPdSceneService sceneService;

    @Autowired
    private IPdLedgerService iPdLedgerService;

    @Autowired
    private IPdLinkInfoService iPdLinkInfoService;
    @Autowired
    private IPdCasualtyInfoService iPdCasualtyInfoService;
    @Autowired
    private IPdAddedService iPdAddedService;
    @Autowired
    private IPdCarInfoService iPdCarInfoService;
    @Resource
    private IPdChatSourceService chatSourceService;
    @Autowired
    private IDailyConfigService dailyConfigService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IPdCarInfoRelService pdCarInfoRelService;

    @Autowired
    private IClickReportHourlyService clickReportHourlyService;

    @Autowired
    private ISysTenantService sysTenantService;

    @Autowired
    private IEaRegionService eaRegionService;

    @Autowired
    private ISysCityPlatePrefixService sysCityPlatePrefixService;

    @Autowired
    private IPdLinkRecodeService pdLinkRecodeService;
    /**
    * 新增
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickAutoPre add(ClickAutoPre dto){
        save(dto);
        return dto;
    }

    /**
    * 修改
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickAutoPre edit(ClickAutoPre dto){
        updateById(dto);
        return dto;
    }

    /**
    * 删除
    *
    * @param id 主键
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id){
        baseMapper.deleteById(id);
    }

    /**
    * 根据id获取 详情
    *
    * @param id 主键
    */
    @Override
    public ClickAutoPre queryById(String id){
         return checkEntity(id);
    }


    /**
    * 分页查询
    *
    * @param dto 参数
    * @return
    */
    @Override
    public IPage<ClickAutoPre>  findPage(IPage<ClickAutoPre> page,ClickAutoPre dto) {
        return page;
    }

    /**
    * 根据id获取 实体
    *
    * @param id 主键
    */
    @Override
    public ClickAutoPre checkEntity(String id){
        if (StringUtils.isBlank(id)){
            throw new IllegalArgumentException("点击数预生成未查询到");
        }
        ClickAutoPre entity = baseMapper.selectById(id);

        return entity;
    }
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void autoCreateClickPreAll() {
        log.info("--点击数预生成开始--");

        try {
            // 1. 获取所有配置
            List<DailyConfig> dailyConfigList = dailyConfigService.lambdaQuery().list();

            if (dailyConfigList.isEmpty()) {
                log.info("没有找到任何配置，跳过处理");
                return;
            }

            log.info("找到{}个配置，开始异步处理", dailyConfigList.size());

            // 2. 将配置信息存入Redis队列
            String redisKey = "click_pre_batch_process:configs";
            String processingKey = "click_pre_batch_process:processing";
            String currentConfigKey = "click_pre_batch_process:current_config";
            String completedConfigsKey = "click_pre_batch_process:completed_configs";

            // 清空之前的配置（如果有）
            redisTemplate.delete(redisKey);
            redisTemplate.delete(processingKey);
            redisTemplate.delete(currentConfigKey);
            redisTemplate.delete(completedConfigsKey);

            // 将每个配置转换为JSON并存入Redis
            for (DailyConfig dailyConfig : dailyConfigList) {
                // 创建配置信息对象
                Map<String, Object> configInfo = new HashMap<>();
                configInfo.put("id", dailyConfig.getId());
                configInfo.put("tenantId", dailyConfig.getTenantId());
                configInfo.put("configJson", dailyConfig.getConfigJson());
                configInfo.put("clickStart", dailyConfig.getClickStart());
                configInfo.put("clickEnd", dailyConfig.getClickEnd());

                // 获取租户名称
                String tenantName = "未知租户";
                try {
                    SysTenant tenant = sysTenantService.getById(dailyConfig.getTenantId());
                    if (tenant != null) {
                        tenantName = tenant.getName();
                    }
                } catch (Exception e) {
                    log.error("获取租户名称失败", e);
                    tenantName = "租户" + dailyConfig.getTenantId();
                }
                configInfo.put("tenantName", tenantName);

                // 计算点击数平均值
                int avgClicks = (dailyConfig.getClickStart() + dailyConfig.getClickEnd()) / 2;
                configInfo.put("avgClicks", avgClicks);

                // 存入Redis队列
                redisTemplate.opsForList().rightPush(redisKey, objectMapper.writeValueAsString(configInfo));
            }

            // 记录初始任务总数
            Long totalTasks = redisTemplate.opsForList().size(redisKey);
            redisTemplate.opsForValue().set("click_pre_batch_process:total_tasks", totalTasks.toString());

            // 设置Redis过期时间到明天结束
            Calendar tomorrow = Calendar.getInstance();
            tomorrow.add(Calendar.DAY_OF_MONTH, 1);
            tomorrow.set(Calendar.HOUR_OF_DAY, 23);
            tomorrow.set(Calendar.MINUTE, 59);
            tomorrow.set(Calendar.SECOND, 59);

            long secondsUntilTomorrowEnd = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

            redisTemplate.expire(redisKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire("click_pre_batch_process:total_tasks", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(currentConfigKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(completedConfigsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);

            log.info("成功将{}个配置加入处理队列，总任务数={}", dailyConfigList.size(), totalTasks);

            // 3. 启动异步处理任务
            processClickPreConfigsAsync();

            log.info("点击数预生成异步任务已启动");

        } catch (Exception e) {
            log.error("点击数预生成初始化失败", e);
            throw new JeecgBootException("点击数预生成初始化失败: " + e.getMessage());
        }
    }
    /**
     * 根据配置预生成数据
     * @param dailyConfig dailyConfig
     * @throws JsonProcessingException
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void autoCreateClickPre(DailyConfig dailyConfig) throws JsonProcessingException {
        log.info("--点击数开始--" + dailyConfig.getId());
        // 将 dailyConfig 的 JSON 配置内容转换为 DailyConfigContent 对象
        DailyConfigContent configContent = objectMapper.readValue(dailyConfig.getConfigJson(), DailyConfigContent.class);

        // 获取目标日期，这里是当前日期的下一天
        LocalDate targetDate = LocalDate.now().plusDays(1);


        // 用来存储生成的 ClickAutoPre 列表
        List<ClickAutoPre> preList = new ArrayList<>();
        if (!Objects.isNull(configContent.getFinanceLedger())) {
            preList.addAll(createTypeList(1, dailyConfig, configContent,configContent.getFinanceLedger(), targetDate));
        }
        if (!Objects.isNull(configContent.getCarLedger())) {
            preList.addAll(createTypeList(3, dailyConfig, configContent,configContent.getCarLedger(), targetDate));
        }
        if (!Objects.isNull(configContent.getValueAddedLedger())) {
            preList.addAll(createTypeList(2, dailyConfig, configContent,configContent.getValueAddedLedger(), targetDate));
        }

        saveBatch(preList);
        log.info("--点击数预结束--" + preList.size());
    }

    public List<ClickAutoPre> createTypeList(Integer ledgerType,DailyConfig dailyConfig,DailyConfigContent configContent,DailyConfigContent.Range range,LocalDate targetDate){
        Map<String, LocalDateTime> resultMap = RandDayNumUtil.generateTimeMap(targetDate, dailyConfig.getClickStart(), dailyConfig.getClickEnd());
        Map<String, LocalDateTime> ledgerTimeMap = RandDayNumUtil.pickByRandomRate( resultMap, range.getLedgerStart(), range.getLedgerEnd());
        Map<String, LocalDateTime> chatUserTimeMap = RandDayNumUtil.pickByRandomRate( ledgerTimeMap, range.getChatUserStart(), range.getChatUserEnd());

        List<ClickAutoPre> preList = new ArrayList<>();
        for (String key : resultMap.keySet()){
            ClickAutoPre autoPre = new ClickAutoPre();
            autoPre.setTenantId(dailyConfig.getTenantId());
            autoPre.setClickTime(Timestamp.valueOf(resultMap.get(key)));
            if (ledgerTimeMap.containsKey(key)){
                autoPre.setAutoCreate(1);
            }
            if (chatUserTimeMap.containsKey(key)){
                autoPre.setHasChatUser(1);
            }
           autoPre.setCity(configContent.getRandomCity());
            autoPre.setLedgerType(ledgerType);
            preList.add(autoPre);
        }
        return preList;
    }

    /**
     * 异步处理点击数预生成配置
     * 从Redis队列中逐个取出配置并处理
     */
    public void processClickPreConfigsAsync() {
        // 创建并启动一个新线程来处理队列
        Thread processThread = new Thread(() -> {
            String redisKey = "click_pre_batch_process:configs";
            String processingKey = "click_pre_batch_process:processing";
            String currentConfigKey = "click_pre_batch_process:current_config";
            String completedConfigsKey = "click_pre_batch_process:completed_configs";

            try {
                // 设置处理状态为正在处理
                redisTemplate.opsForValue().set(processingKey, "1");

                // 设置Redis过期时间到明天结束
                Calendar tomorrow = Calendar.getInstance();
                tomorrow.add(Calendar.DAY_OF_MONTH, 1);
                tomorrow.set(Calendar.HOUR_OF_DAY, 23);
                tomorrow.set(Calendar.MINUTE, 59);
                tomorrow.set(Calendar.SECOND, 59);

                long secondsUntilTomorrowEnd = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                // 设置Redis过期时间
                redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(redisKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire("click_pre_batch_process:total_tasks", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(currentConfigKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(completedConfigsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);

                while (true) {
                    // 从队列左侧弹出一个配置（先进先出）
                    String configJson = (String) redisTemplate.opsForList().leftPop(redisKey);

                    // 如果队列为空，处理完成
                    if (configJson == null) {
                        // 清除当前处理的配置信息
                        redisTemplate.delete(currentConfigKey);
                        break;
                    }

                    try {
                        // 解析配置信息
                        Map<String, Object> configInfo = objectMapper.readValue(configJson,
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 记录当前处理的配置信息
                        Map<String, Object> currentConfigInfo = new HashMap<>();
                        currentConfigInfo.put("tenantId", configInfo.get("tenantId"));
                        currentConfigInfo.put("tenantName", configInfo.get("tenantName"));
                        currentConfigInfo.put("avgClicks", configInfo.get("avgClicks"));
                        currentConfigInfo.put("startTime", new Date());
                        currentConfigInfo.put("status", "处理中");

                        redisTemplate.opsForValue().set(currentConfigKey, objectMapper.writeValueAsString(currentConfigInfo));

                        log.info("开始处理配置: 租户ID={}, 租户名称={}",
                            configInfo.get("tenantId"), configInfo.get("tenantName"));

                        // 重新构建DailyConfig对象
                        DailyConfig dailyConfig = new DailyConfig();
                        dailyConfig.setId((String) configInfo.get("id"));
                        dailyConfig.setTenantId((Integer) configInfo.get("tenantId"));
                        dailyConfig.setConfigJson((String) configInfo.get("configJson"));
                        dailyConfig.setClickStart((Integer) configInfo.get("clickStart"));
                        dailyConfig.setClickEnd((Integer) configInfo.get("clickEnd"));

                        // 处理配置
                        autoCreateClickPre(dailyConfig);

                        // 记录完成时间
                        Date endTime = new Date();

                        // 创建已完成的配置信息
                        Map<String, Object> completedConfigInfo = new HashMap<>();
                        completedConfigInfo.put("tenantId", configInfo.get("tenantId"));
                        completedConfigInfo.put("tenantName", configInfo.get("tenantName"));
                        completedConfigInfo.put("avgClicks", configInfo.get("avgClicks"));
                        completedConfigInfo.put("startTime", currentConfigInfo.get("startTime"));
                        completedConfigInfo.put("endTime", endTime);
                        completedConfigInfo.put("status", "已完成");

                        // 将已完成的配置信息添加到列表
                        redisTemplate.opsForList().rightPush(completedConfigsKey, objectMapper.writeValueAsString(completedConfigInfo));

                        log.info("配置处理完成: 租户ID={}, 租户名称={}",
                            configInfo.get("tenantId"), configInfo.get("tenantName"));

                        // 添加适当的延迟，避免系统负载过高
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("处理配置失败: {}", configJson, e);
                        // 处理失败的配置放回队列末尾，以便稍后重试
                        redisTemplate.opsForList().rightPush(redisKey, configJson);
                        // 添加延迟，避免立即重试
                        Thread.sleep(5000);
                    }
                }

                // 所有任务处理完成，设置状态为完成
                redisTemplate.opsForValue().set(processingKey, "0");
                log.info("所有点击数预生成配置处理完成");

            } catch (Exception e) {
                log.error("点击数预生成配置批处理失败", e);
                // 设置处理状态为失败
                try {
                    redisTemplate.opsForValue().set(processingKey, "-1");
                } catch (Exception ex) {
                    log.error("设置处理状态失败", ex);
                }
            }
        });

        // 设置为守护线程，不阻止JVM退出
        processThread.setDaemon(true);
        processThread.setName("ClickPreBatchProcessor");
        processThread.start();
    }

    /**
     * 每日定时生成方法
     * @throws ParseException
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void autoCreateClickAll() throws ParseException {
        List<DailyConfig> dailyConfigList = dailyConfigService.lambdaQuery().list();
        for (DailyConfig dailyConfig : dailyConfigList){
            autoCreateClick( dailyConfig);
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void autoCreateClick(DailyConfig dailyConfig) throws ParseException {
        long now = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 当天 00:00:00
        Date startDate = sdf.parse(sdf.format(new Date(now)));

        // 查询 click_time 在 [今天 00:00:00, 当前时间)，且 companyId 相等
        List<ClickAutoPre> preList = lambdaQuery()
                .eq(ClickAutoPre::getTenantId, dailyConfig.getTenantId())
                .ge(ClickAutoPre::getClickTime, new Timestamp(startDate.getTime()))
                .lt(ClickAutoPre::getClickTime, new Timestamp(now))  // 小于当前时间
                .list();

        ClickReport clickReport= clickReportService.lambdaQuery()
                .eq(ClickReport::getStatDate,startDate)
                .eq(ClickReport::getTenantId,dailyConfig.getTenantId())
                .last("limit 1")
                .one();
        if (clickReport == null){
            clickReport = new ClickReport();
            clickReport.setStatDate(startDate);
            clickReport.setClickNum(preList.size());
            clickReport.setTenantId(dailyConfig.getTenantId());
            clickReportService.save(clickReport);
        }else {
            clickReport.setClickNum(clickReport.getClickNum()+preList.size());
            clickReportService.updateById(clickReport);
        }

        List<ClickReportHourly> clickReportHourList= clickReportHourlyService.lambdaQuery()
                .eq(ClickReportHourly::getStatDate,startDate)
                .eq(ClickReportHourly::getTenantId,dailyConfig.getTenantId())
                .list();
        Map<String, ClickReportHourly> clickReportHourlyMap = CollectionUtils.isEmpty(clickReportHourList)
                ? new HashMap<>()
                : clickReportHourList.stream().collect(Collectors.toMap(
                item -> item.getStatDate().toString() + item.getHour(), // key: yyyy-MM-dd + hour
                item -> item,
                (existing, replacement) -> existing // 若有重复 key，保留原始的
        ));

        List<ClickReportHourly> saveList = new ArrayList<>();
        for (ClickAutoPre clickAutoPre : preList){
            Timestamp clickTime = clickAutoPre.getClickTime();
            // 格式化为 yyyy-MM-dd
            String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(clickTime);
            // 获取小时（0-23）
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(clickTime);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            // 构造 key
            String key = dateStr + hour;
            if (clickReportHourlyMap.containsKey(key)) {
                ClickReportHourly clickReportHourly = clickReportHourlyMap.get(key);
                clickReportHourly.setClickNum(clickReportHourly.getClickNum() + 1);
            }else {
                ClickReportHourly clickReportHourly = new ClickReportHourly();

                // 将格式化字符串再转回 java.util.Date（代表当天 00:00:00）
                clickReportHourly.setStatDate(sdf.parse(dateStr));
                clickReportHourly.setHour(hour);
                clickReportHourly.setClickNum(1);
                clickReportHourly.setTenantId(dailyConfig.getTenantId());
                saveList.add(clickReportHourly);
                clickReportHourlyMap.put(key, clickReportHourly);
            }
        }
        if (!CollectionUtils.isEmpty(saveList)){
            clickReportHourlyService.saveBatch(saveList);
        }
        if (!CollectionUtils.isEmpty(clickReportHourList)){
            clickReportHourlyService.updateBatchById(clickReportHourList);
        }
        if (CollectionUtils.isEmpty(preList)){
            return;
        }
        List<ClickAutoPre> clickAutoPreList = preList.stream().filter(clickAutoPre -> clickAutoPre.getAutoCreate() == 1).collect(Collectors.toList());
        autoCreateLedger(clickAutoPreList, dailyConfig.getTenantId());
        this.baseMapper.deleteBatchIds(preList.stream().map(ClickAutoPre::getId).collect(Collectors.toList()));
    }
    /**
     *
     */

    /**
     * 分批处理台账生成
     * @param preList 点击预生成列表
     * @param tenantId 租户ID
     * @param batchSize 每批处理的数量
     */
    @Transactional(propagation = Propagation.NEVER) // 禁止事务传播，确保每个批次有自己的事务
    public void batchCreateLedger(List<ClickAutoPre> preList, Integer tenantId, int batchSize) {
        if (CollectionUtils.isEmpty(preList)) {
            return;
        }

        // 如果批次大小无效，设置默认值
        if (batchSize <= 0) {
            batchSize = 3000; // 默认每批3000条
        }

        // 计算总批次数
        int totalSize = preList.size();
        int batchCount = (totalSize + batchSize - 1) / batchSize;


        // 分批处理
        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min(fromIndex + batchSize, totalSize);
            List<ClickAutoPre> batchList = preList.subList(fromIndex, toIndex);

            log.info("处理第 {}/{} 批，数据量：{}", i + 1, batchCount, batchList.size());

            try {
                // 在新的事务中处理每个批次
                processLedgerBatch(batchList, tenantId);
                log.info("第 {}/{} 批处理完成", i + 1, batchCount);
            } catch (Exception e) {
                log.error("第 {}/{} 批处理失败", i + 1, batchCount, e);
                // 这里可以选择继续处理下一批，或者抛出异常中断整个处理
                // throw new RuntimeException("批处理失败", e);
            }
        }

    }

    /**
     * 在新事务中处理一个批次的台账生成
     * @param batchList 批次数据
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class) // 确保每个批次在单独的事务中
    public void processLedgerBatch(List<ClickAutoPre> batchList, Integer tenantId) {
        // 调用原有的处理逻辑
        autoCreateLedgerInternal(batchList, tenantId);
    }

    /**
     * 生成台账（原方法，为了兼容性保留）
     * @param preList 点击预生成列表
     * @param tenantId 租户ID
     */
    public void autoCreateLedger(List<ClickAutoPre> preList, Integer tenantId) {
        // 调用分批处理方法，默认每批3000条数据
        batchCreateLedger(preList, tenantId, 2000);
    }

    /**
     * 生成台账（内部方法，由批处理方法调用）
     * @param preList 点击预生成列表
     * @param tenantId 租户ID
     */
    private void autoCreateLedgerInternal(List<ClickAutoPre> preList, Integer tenantId) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理批次，数据量：{}", preList.size());

        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        // 为每个车辆创建服务关系
        Random random = new Random();
        if (deployConfig == null) {
            return;
        }
        boolean configChange = false;
        if (deployConfig.getQueryType().equals("db")) {
            configChange = true;
        }
        //台账类型(1.财险台账 2.增值服务台账 3.车险台账)
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<Integer,List<ClickAutoPre>> mapByType = preList.stream().collect(Collectors.groupingBy(ClickAutoPre::getLedgerType));
        List<PdInsuranceLedger> pdInsuranceLedgerList = new ArrayList<>();
        List<PdAddedLedger> pdAddedLedgerList = new ArrayList<>();
        List<PdLedger> pdLedgerList = new ArrayList<>();
        List<PdCasualtyInfo> pdCasualtyInfoList = new ArrayList<>();
        List<PdAdded> pdAddedList = new ArrayList<>();
        List<PdCarInfo> carInfoList = new ArrayList<>();
        List<PdCarInfoRel> carInfoRelList = new ArrayList<>(); // 新增车辆服务关系列表
        List<PdLinkInfo> pdLinkInfoList = iPdLinkInfoService.lambdaQuery().eq(PdLinkInfo::getTenantId, tenantId).list();
        Map<Integer,PdLinkInfo> pdLinkInfoMap = pdLinkInfoList.stream().collect(Collectors.toMap(PdLinkInfo::getLinkType, Function.identity(), (k1,k2)->k1));
        for (Integer ledgerType : mapByType.keySet()){
            List<ClickAutoPre> listByType = mapByType.get(ledgerType);
            for (ClickAutoPre clickAutoPre : listByType){
                String name = sceneService.getRandomName();

                // 根据城市编码获取城市信息
                CityInfoVO cityInfo = getCityInfoByCityCode(clickAutoPre.getCity());

                // 获取车牌号、IP地址等信息
                String licensePlate = cityInfo != null ? cityInfo.getLicensePlate() : ChinaPlateNumberGenerator.generatePlateNumber(clickAutoPre.getCity());

                // 生成完整的手机号
                String maskedPhone = ProvinceIpGenerator.generateMaskedPhone();
                // 生成完整的车架号
                String vin = RandomVinGenerator.generateRandomVin();
                if (configChange) {
                    if (deployConfig.getNameSwitch()) {
                        // 截取名字的第一个字（姓）
                        String surname = name.substring(0, 1);
                        // 随机选择“先生”或“女士”
                        String title = random.nextBoolean() ? "先生" : "女士";  // 随机选择
                        name = surname + title;
                    }
                    if (deployConfig.getPlateNoMask() && licensePlate != null && licensePlate.length() >= 7) {
                        try {
                            // 截取车牌号的前4位，例如“粤A12”
                            String prefix = licensePlate.substring(0, 4);

                            // 截取后两位，例如“45”
                            String suffix = licensePlate.substring(6);

                            // 拼接脱敏后的车牌号
                            String maskedPlate = prefix + "**" + suffix;

                            // 去除所有空白字符（空格、换行、制表符等）
                            licensePlate = maskedPlate.replaceAll("\\s+", "");
                        } catch (Exception e) {
                            // 如果格式异常，保留原始值但清除空白
                            licensePlate = licensePlate.replaceAll("\\s+", "");
                        }
                    }
                    // 如果需要进行手机号脱敏，判断条件
                    if (deployConfig.getPhoneSwitch()) {
                        // 对手机号进行脱敏处理，替换中间四位为星号
                        maskedPhone = maskedPhone.substring(0, 3) + "****" + maskedPhone.substring(7);
                    }
                    if (deployConfig.getVinNoMask()) {
                        // 获取车架号的前8位（例如：1HGCM826）
                        String prefix = vin.substring(0, 8);  // 第0到第7位

                        // 获取车架号的后4位（例如：A123456）
                        String suffix = vin.substring(12);  // 从第12位到最后

                        // 中间四位替换为星号
                        String maskedVin = prefix + "****" + suffix;

                        // 返回脱敏后的车架号
                        vin = maskedVin;
                    }
                }
                //1.财险台账
                if (ledgerType == 1){
                    PdInsuranceLedger pdInsuranceLedger = new PdInsuranceLedger();
                    pdInsuranceLedger.setOrderDate(new java.sql.Date(clickAutoPre.getClickTime().getTime()));
                    pdInsuranceLedger.setName(name);
                    pdInsuranceLedger.setPhone(maskedPhone);
                    //服务
                    String randomServiceId = pdIntegratedService.getRandomServiceId(1);
                    pdInsuranceLedger.setUserItem(randomServiceId);
                    pdInsuranceLedger.setTenantId(clickAutoPre.getTenantId());
                    pdInsuranceLedgerList.add(pdInsuranceLedger);
                    pdInsuranceLedger.setHasChatUser(clickAutoPre.getHasChatUser());
                    pdCasualtyInfoList.add(createPdCasualtyInfo( pdInsuranceLedger, clickAutoPre.getCity(), pdLinkInfoMap));
                }
                //2.增值服务台账
                if (ledgerType == 2){
                    PdAddedLedger pdAddedLedger = new PdAddedLedger();
                    pdAddedLedger.setOrderDate(new java.sql.Date(clickAutoPre.getClickTime().getTime()));
                    pdAddedLedger.setName(name);
                    pdAddedLedger.setPhone(maskedPhone);
                    //服务
                    String randomServiceId = pdIntegratedService.getRandomServiceId(2);
                    //todo
                    pdAddedLedger.setUserItem(randomServiceId);
                    pdAddedLedger.setTenantId(clickAutoPre.getTenantId());
                    pdAddedLedgerList.add(pdAddedLedger);
                    pdAddedLedger.setHasChatUser(clickAutoPre.getHasChatUser());
                    pdAddedList.add(createPdAdded( pdAddedLedger, clickAutoPre.getCity(), pdLinkInfoMap,cityInfo));
                }
                //3.车险台账
                if (ledgerType == 3){
                    PdLedger pdLedger = new PdLedger();
                    pdLedger.setInsuranceName(RandomInsuranceTypeGenerator.generateRandomInsuranceType());
                    pdLedger.setPhoneNumber(maskedPhone);
                    pdLedger.setSignDate(dateFormat.format(clickAutoPre.getClickTime()));
                    pdLedger.setSignDateTime(clickAutoPre.getClickTime());
                    pdLedger.setPolicyholder(name);
                    pdLedger.setInsured(name);

                    pdLedger.setLicensePlate(licensePlate);
                    pdLedger.setBrandModel(RandomCarBrandGenerator.generateRandomCarBrand());
                    pdLedger.setVin(vin);
                    pdLedger.setIsDelete("0");
                    pdLedger.setChatStatus(2);
                    pdLedger.setTenantId(clickAutoPre.getTenantId());
                    pdLedgerList.add(pdLedger);
                    pdLedger.setHasChatUser(clickAutoPre.getHasChatUser());

                    // 使用城市信息VO中的数据
                    if (cityInfo != null) {
                        // 设置城市信息到pdLedger
                        pdLedger.setCity(cityInfo.getFullCityName());
                        pdLedger.setIpAddress(cityInfo.getIpAddress());
                    }

                    // 创建车辆信息
                    PdCarInfo carInfo = createCarInfo(pdLedger, random, new PdGuestUsers());
                    if (carInfo != null) {
                        carInfoList.add(carInfo);
                    }

                }
            }
        }
//        if (!CollectionUtils.isEmpty(pdInsuranceLedgerList)){
//            iPdInsuranceLedgerService.saveBatch(pdInsuranceLedgerList);
//        }
//        if (!CollectionUtils.isEmpty(pdAddedLedgerList)){
//            iPdAddedLedgerService.saveBatch(pdAddedLedgerList);
//        }
//        if (!CollectionUtils.isEmpty(pdLedgerList)){
//            iPdLedgerService.saveBatch(pdLedgerList);
//        }

        if (!CollectionUtils.isEmpty(pdCasualtyInfoList)){
            iPdCasualtyInfoService.saveBatch(pdCasualtyInfoList);
            updateChatCasualty(tenantId, pdCasualtyInfoList);
        }
        if (!CollectionUtils.isEmpty(pdAddedList)){
            iPdAddedService.saveBatch(pdAddedList);
            updateChatPdAdded( tenantId, pdAddedList);
        }
        if (!CollectionUtils.isEmpty(carInfoList)){
            // 批量保存车辆信息
            iPdCarInfoService.saveBatch(carInfoList);

            // 获取所有可用的服务列表（只查询一次数据库）
            List<PdIntegrated> integratedList = pdIntegratedService.list(
                new LambdaQueryWrapper<PdIntegrated>()
                    .eq(PdIntegrated::getType, 0) // 假设类型3是车辆服务
            );


            for (PdCarInfo carInfo : carInfoList) {
                // 为每辆车创建3-5个随机关系
                int relCount = random.nextInt(3) + 3;

                // 创建一个打乱的服务列表副本，以获取随机服务
                List<PdIntegrated> shuffledList = new ArrayList<>(integratedList);
                Collections.shuffle(shuffledList, random);

                // 为每辆车创建指定数量的服务关系
                for (int j = 0; j < Math.min(relCount, shuffledList.size()); j++) {
                    PdIntegrated integrated = shuffledList.get(j);
                    PdCarInfoRel pdCarInfoRel = new PdCarInfoRel();

                    // 根据input_type决定是否设置金额
                    if ("Y".equals(integrated.getInputType())) {
                        // 如果开启了输入，则设置随机金额
                        pdCarInfoRel.setAmount(random.nextInt(8) + 3);
                    } else {
                        // 如果没有开启输入，则不设置金额
                        pdCarInfoRel.setAmount(null);
                    }

                    pdCarInfoRel.setCarInfoId(carInfo.getId())
                            .setMercialId(integrated.getId())
                            .setUserId(carInfo.getGuestId());
                    carInfoRelList.add(pdCarInfoRel);
                }
            }

            // 批量保存所有服务关系
            if (!carInfoRelList.isEmpty()) {
                pdCarInfoRelService.saveBatch(carInfoRelList);
            }

            updateChatCarInfo(tenantId, carInfoList);
        }

        long endTime = System.currentTimeMillis();
        log.info("批次处理完成，数据量：{}，耗时：{}ms", preList.size(), (endTime - startTime));
    }

    public void updateChatCasualty(Integer tenantId,List<PdCasualtyInfo> pdCasualtyInfoList){
        PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        if (deployConfig == null) {
            return;
        }
        String serviceIpType = deployConfig.getServiceIpType();
        String ipAddress = "";
        if (serviceIpType.equals("1")){
            ipAddress = chatSourceService.getIpAddressByTenantId(tenantId);
            if (StrUtil.isEmpty(ipAddress)) {
                ipAddress = deployConfig.getServerIp();
            }
        }else {
            //取服务器 ip
            ipAddress = deployConfig.getServerIp();
        }
        pdBatchChatDto.setIpAddress(ipAddress);
        pdBatchChatDto.setTenantId(tenantId);
        List<LedgerChatDto> chatDtoList = new ArrayList<>();
        pdCasualtyInfoList.forEach(entity -> {
            if (Objects.equals(entity.getHasChatUser(),1)){
                LedgerChatDto chatDto = new LedgerChatDto();
                chatDto.setId(entity.getId());
                chatDto.setCreateTime(entity.getCreateTime());
                chatDtoList.add(chatDto);
            }

        });
        pdBatchChatDto.setPidList(chatDtoList);
        chatSourceService.createChatUser(pdBatchChatDto);
        // 更新车辆信息
        iPdCarInfoService.updateByCasualty(
                chatDtoList.stream().map(LedgerChatDto::getId).collect(Collectors.toList()));
    }

    public void updateChatPdAdded(Integer tenantId,List<PdAdded> pdAddedList){
        PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
        //查询租户 id,赋值 ip地址
        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        if (deployConfig == null) {
            return;
        }
        String serviceIpType = deployConfig.getServiceIpType();
        String ipAddress = "";
        if (serviceIpType.equals("1")){
            ipAddress = chatSourceService.getIpAddressByTenantId(tenantId);
            if (StrUtil.isEmpty(ipAddress)) {
                ipAddress = deployConfig.getServerIp();
            }
        }else {
            //取服务器 ip
            ipAddress = deployConfig.getServerIp();
        }
        pdBatchChatDto.setIpAddress(ipAddress);
        pdBatchChatDto.setTenantId(tenantId);
        List<LedgerChatDto> chatDtoList = new ArrayList<>();
        pdAddedList.forEach(entity -> {
            if (Objects.equals(entity.getHasChatUser(),1)){
                LedgerChatDto chatDto = new LedgerChatDto();
                chatDto.setId(entity.getId());
                chatDto.setCreateTime(entity.getCreateTime());
                chatDtoList.add(chatDto);
            }

        });
        pdBatchChatDto.setPidList(chatDtoList);
        chatSourceService.createChatUser(pdBatchChatDto);
        iPdCarInfoService.updateByPdAdded(
                chatDtoList.stream().map(LedgerChatDto::getId).collect(Collectors.toList()));
    }

    public void updateChatCarInfo(Integer tenantId,List<PdCarInfo> carInfoList ){
        PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        if (deployConfig == null) {
            return;
        }
        String serviceIpType = deployConfig.getServiceIpType();
        String ipAddress = "";
        if (serviceIpType.equals("1")){
            ipAddress = chatSourceService.getIpAddressByTenantId(tenantId);
            if (StrUtil.isEmpty(ipAddress)) {
                ipAddress = deployConfig.getServerIp();
            }
        }else {
            //取服务器 ip
            ipAddress = deployConfig.getServerIp();
            if (StrUtil.isEmpty(ipAddress)) {
                ipAddress = "**************";
            }
        }
        pdBatchChatDto.setIpAddress(ipAddress);
        pdBatchChatDto.setTenantId(tenantId);
        List<LedgerChatDto> chatDtoList = new ArrayList<>();
        carInfoList.forEach(entity -> {
            if (Objects.equals(entity.getHasChatUser(),1)){
                LedgerChatDto chatDto = new LedgerChatDto();
                chatDto.setId(entity.getId());
                chatDto.setCreateTime(entity.getCreateTime());
                chatDtoList.add(chatDto);
            }

        });
        pdBatchChatDto.setPidList(chatDtoList);
        chatSourceService.createChatUser(pdBatchChatDto);
        // 更新车辆信息
        //iPdCarInfoService.updateByPid(chatDtoList.stream().map(LedgerChatDto::getId).collect(Collectors.toList()));
    }


    //财险预约信息
    public PdCasualtyInfo createPdCasualtyInfo(PdInsuranceLedger pdInsuranceLedger,String province,Map<Integer,PdLinkInfo> pdLinkInfoMap) {
        PdCasualtyInfo pdCasualtyInfo = new PdCasualtyInfo();
        pdCasualtyInfo.setLinkType(1); // 0-车险，1-财险，2-增值服务
        String provinceByLocation = ChinaPlateNumberGenerator.getProvinceByLocation(province);
        String address = LicensePlateToProvince.getProvinceFromLicensePlate(provinceByLocation.substring(0, 1));
        String ipForProvince = ProvinceIpGenerator.getRandomIpForProvince(address);
        pdCasualtyInfo.setIpAddress(ipForProvince); // IP地址
        pdCasualtyInfo.setSource(random.nextInt(2)); // 用户来源，具体值视业务而定
        pdCasualtyInfo.setName(pdInsuranceLedger.getName()); // 姓名
        pdCasualtyInfo.setPhone(pdInsuranceLedger.getPhone()); // 手机号
        pdCasualtyInfo.setLinkId(null); // 链接ID
        //todo
        pdCasualtyInfo.setServe(pdInsuranceLedger.getUserItem()); // 选择服务
        pdCasualtyInfo.setTenantId(pdInsuranceLedger.getTenantId()); // 多租户ID
        pdCasualtyInfo.setIsVied(1); // 是否已生成：0 未生成，1 已生成
        pdCasualtyInfo.setIsDelete(0); // 是否删除：0 正常，1 删除
        pdCasualtyInfo.setCreateTime(generateRandomCreateTime(pdInsuranceLedger.getOrderDate()));
        pdCasualtyInfo.setHasChatUser(pdInsuranceLedger.getHasChatUser());
        return pdCasualtyInfo;
    }
    //增值服务预约记录
    public PdAdded createPdAdded(PdAddedLedger pdAddedLedger, String province, Map<Integer,PdLinkInfo> pdLinkInfoMap, CityInfoVO cityInfo) {

        PdAdded pdAdded = new PdAdded();

        pdAdded.setLinkType(2); // 链接类型：0-车险、1-财险、2-增值等
        pdAdded.setIpAddress(cityInfo.getIpAddress()); // IP地址
        pdAdded.setSource(random.nextInt(2)); // 用户来源（自定义）
        pdAdded.setName(pdAddedLedger.getName()); // 姓名
        pdAdded.setPhone(pdAddedLedger.getPhone()); // 手机号
        pdAdded.setLinkId(null); // 链接ID
        //todo
        pdAdded.setServe(pdAddedLedger.getUserItem()); // 服务名
        pdAdded.setIsVied(1); // 是否已生成：0 否，1 是
        pdAdded.setTenantId(pdAddedLedger.getTenantId()); // 多租户 ID
        pdAdded.setCreateTime(generateRandomCreateTime(pdAddedLedger.getOrderDate()));
        pdAdded.setHasChatUser(pdAddedLedger.getHasChatUser());
        return pdAdded;
    }
    public PdCarInfo createCarInfo(PdLedger pdLedger, Random random, PdGuestUsers guestUser) {
        //根据姓名判断是否为男女,如果没有带女士,则随机姓名即可
        String insured = pdLedger.getInsured();
        String sex = random.nextBoolean() ? "男" : "女";
        if (insured.contains("女士")) {
            sex ="女";
        } else if (insured.contains("先生")) {
            sex ="男";
        }
        String licensePlate = pdLedger.getLicensePlate();

//        String address = LicensePlateToProvince.getProvinceFromLicensePlate(licensePlate.substring(0, 1));
//        String city = LicensePlateToProvince.getRandomCity(address);
//        String ipForProvince = ProvinceIpGenerator.getRandomIpForProvince(address);
        //Date date = generateRandomCreateTime(pdLedger.getSignDateTime());

        return new PdCarInfo()
                .setCity(pdLedger.getCity())
                .setModel(pdLedger.getBrandModel())
                .setLicensePlateNumber(licensePlate)
                .setVinCode(pdLedger.getVin())
                .setOwner(pdLedger.getInsured())
                .setIpAddress(pdLedger.getIpAddress())
                .setPhoneNumber(pdLedger.getPhoneNumber())
                .setLedgerId(pdLedger.getId())
                .setCreateTime(pdLedger.getSignDateTime())
                .setSex(sex)
                .setIsVied(2)
                .setTenantId(pdLedger.getTenantId())
                .setHasChatUser(pdLedger.getHasChatUser())
                .setGuestId(guestUser.getId());
    }
    private Date generateRandomCreateTime(Date signDateTime) {
        Random random = new Random();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(signDateTime); // 基于 signDateTime 的日期

        // 5% 概率落在 0-6 点，其余 95% 落在 6-24 点
        int hour;
        if (random.nextDouble() < 0.05) {
            hour = random.nextInt(6); // 0-5 点
        } else {
            hour = 6 + random.nextInt(18); // 6-23 点（23:59:59 仍有效）
        }

        int minute = random.nextInt(60); // 0-59 分
        int second = random.nextInt(60); // 0-59 秒

        // 设置随机时间
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒清零，避免额外误差

        return calendar.getTime();
    }

    /**
     * 根据省份名称获取车牌前缀
     *
     * @param provinceName 省份名称
     * @return 车牌前缀，如"京A"、"沪B"等
     */
    private String getPlatePrefix(String provinceName) {
        if (StringUtils.isBlank(provinceName)) {
            return null;
        }

        // 省份简称映射
        Map<String, String> provinceMap = new HashMap<>();
        provinceMap.put("北京", "京");
        provinceMap.put("天津", "津");
        provinceMap.put("上海", "沪");
        provinceMap.put("重庆", "渝");
        provinceMap.put("河北", "冀");
        provinceMap.put("山西", "晋");
        provinceMap.put("辽宁", "辽");
        provinceMap.put("吉林", "吉");
        provinceMap.put("黑龙江", "黑");
        provinceMap.put("江苏", "苏");
        provinceMap.put("浙江", "浙");
        provinceMap.put("安徽", "皖");
        provinceMap.put("福建", "闽");
        provinceMap.put("江西", "赣");
        provinceMap.put("山东", "鲁");
        provinceMap.put("河南", "豫");
        provinceMap.put("湖北", "鄂");
        provinceMap.put("湖南", "湘");
        provinceMap.put("广东", "粤");
        provinceMap.put("海南", "琼");
        provinceMap.put("四川", "川");
        provinceMap.put("贵州", "贵");
        provinceMap.put("云南", "云");
        provinceMap.put("西藏", "藏");
        provinceMap.put("陕西", "陕");
        provinceMap.put("甘肃", "甘");
        provinceMap.put("青海", "青");
        provinceMap.put("内蒙古", "蒙");
        provinceMap.put("广西", "桂");
        provinceMap.put("宁夏", "宁");
        provinceMap.put("新疆", "新");
        provinceMap.put("香港", "港");
        provinceMap.put("澳门", "澳");
        provinceMap.put("台湾", "台");

        // 遍历省份映射，查找匹配的省份简称
        for (Map.Entry<String, String> entry : provinceMap.entrySet()) {
            if (provinceName.contains(entry.getKey())) {
                // 找到匹配的省份，返回省份简称 + "A"（默认使用A作为发牌机关代号）
                return entry.getValue() + "A";
            }
        }

        // 如果没有找到匹配的省份，返回null
        return null;
    }

    // Redis缓存前缀
    private static final String CITY_INFO_CACHE_PREFIX = "city_info:";

    /**
     * 加载单个城市信息
     *
     * @param region 城市区域信息
     * @return 城市信息VO
     */
    private CityInfoVO loadCityInfo(EaRegion region) {
        if (region == null || StringUtils.isBlank(region.getCode())) {
            return null;
        }

        String cityCode = region.getCode();

        try {
            // 获取完整城市名称和顶级城市名称
            String fullCityName = null;
            String topCityName = null;

            try {
                fullCityName = eaRegionService.getFullCityNameById(region.getId());
                topCityName = eaRegionService.getTopCityNameById(region.getId());
            } catch (Exception e) {
                log.warn("获取城市路径名称失败: {}", e.getMessage());
            }

            if (StringUtils.isBlank(fullCityName)) {
                fullCityName = region.getName();
            }
            if (StringUtils.isNotEmpty(topCityName) && StrUtil.isNotEmpty(fullCityName)) {
                fullCityName = topCityName + fullCityName;
            }

            // 获取车牌前缀
            String platePrefix = sysCityPlatePrefixService.getPlatePrefixByCityCode(cityCode);

            // 生成车牌号
            String licensePlate = null;
            if (StringUtils.isNotBlank(platePrefix)) {
                // 使用车牌前缀生成完整车牌号
                licensePlate = LicensePlateGenerator.generateRandomPlate(platePrefix);
            } else {
                // 如果没有找到车牌前缀，使用城市名称生成车牌号
                String provinceName = region.getName();
                if (StringUtils.isNotBlank(topCityName)) {
                    provinceName = topCityName;
                }
                // 尝试从省份名称中提取车牌前缀
                String prefix = getPlatePrefix(provinceName);
                if (StringUtils.isNotBlank(prefix)) {
                    licensePlate = LicensePlateGenerator.generateRandomPlate(prefix);
                } else {
                    // 如果无法提取车牌前缀，使用旧的方法生成车牌号
                    licensePlate = ChinaPlateNumberGenerator.generatePlateNumber(region.getName());
                }
            }

            // 生成IP地址
            String ipAddress = null;
            try {
                // 直接使用城市编码获取IP地址
                ipAddress = ProvinceIpGenerator.getIpByCityCode(cityCode);
            } catch (Exception e) {
                // 如果通过城市编码获取IP失败，尝试使用省份名称获取
                if (StringUtils.isNotBlank(topCityName)) {
                    ipAddress = ProvinceIpGenerator.getRandomIpForProvince(topCityName);
                }
                log.warn("通过城市编码获取IP地址失败: {}, 使用省份名称获取: {}", e.getMessage(), ipAddress);
            }

            // 创建城市信息VO
            return new CityInfoVO()
                    .setCityCode(cityCode)
                    .setFullCityName(fullCityName)
                    .setTopCityName(topCityName)
                    .setLicensePlate(licensePlate)
                    .setIpAddress(ipAddress);

        } catch (Exception e) {
            log.error("加载城市信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据城市编码获取城市信息
     * 优化版本：使用Redis缓存避免重复查询相同城市信息
     *
     * @param cityCode 城市编码
     * @return 城市信息VO
     */
    private CityInfoVO getCityInfoByCityCode(String cityCode) {
        if (StringUtils.isBlank(cityCode)) {
            return null;
        }

        String cacheKey = CITY_INFO_CACHE_PREFIX + cityCode;
        CityInfoVO cityInfoVO = null;
        // 先从Redis缓存中查找
        try {
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue != null) {
                cityInfoVO = ((CityInfoVO) cachedValue);
                cityInfoVO = cloneAndRefreshCityInfo(cityInfoVO);
                return cityInfoVO;
            }
        } catch (Exception e) {
            log.warn("从Redis获取城市信息失败: {}", e.getMessage());
            // 继续执行，从数据库查询
        }

        // 如果缓存中没有，则查询并加载到缓存
        try {
            EaRegion region = eaRegionService.findByCode(cityCode);
            if (region == null) {
                log.warn("未找到城市编码对应的城市信息: {}", cityCode);
                return null;
            }

            // 加载城市信息
             cityInfoVO = loadCityInfo(region);

            // 将结果存入Redis缓存，设置过期时间为1天（城市信息相对稳定）
            if (cityInfoVO != null) {
                try {
                    redisTemplate.opsForValue().set(cacheKey, cityInfoVO, 24, TimeUnit.HOURS);
                } catch (Exception e) {
                    log.warn("将城市信息存入Redis失败: {}", e.getMessage());
                    // 继续执行，返回查询结果
                }
            }

            return cityInfoVO;
        } catch (Exception e) {
            log.error("获取城市信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    /**
     * 克隆原始城市信息并生成新的 licensePlate 和 ipAddress
     */
    private CityInfoVO cloneAndRefreshCityInfo(CityInfoVO original) {
        CityInfoVO newCityInfoVO = new CityInfoVO();
//        if (!original.getFullCityName().equals(original.getTopCityName()) && StrUtil.isNotEmpty(original.getTopCityName())) {
//            newCityInfoVO.setFullCityName(original.getTopCityName() + original.getFullCityName());
//        }else {
//            newCityInfoVO.setFullCityName(original.getFullCityName());
//        }
        return newCityInfoVO
                .setCityCode(original.getCityCode())
                .setFullCityName(original.getFullCityName())
                .setTopCityName(original.getTopCityName())
                .setLicensePlate(LicensePlateGenerator.generateRandomPlate(original.getLicensePlate().substring(0, 2))) // 自定义生成逻辑
                .setIpAddress(ProvinceIpGenerator.getIpByCityCode(original.getCityCode()));     // 自定义生成逻辑
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importLedgers(List<MultipartFile> files,DailyConfigDto dto) throws JsonProcessingException {
        Date monthDataStartDate = java.sql.Date.from(dto.getMonthDataStart().atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date monthDataEndDate = java.sql.Date.from(dto.getMonthDataEnd().atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 清除点击报表数据
        clickReportService.lambdaUpdate()
                .between(ClickReport::getStatDate, monthDataStartDate, monthDataEndDate)
                .eq(ClickReport::getTenantId,dto.getTenantId())
                .remove();
        clickReportHourlyService.lambdaUpdate()
                .between(ClickReportHourly::getStatDate, monthDataStartDate, monthDataEndDate)
                .eq(ClickReportHourly::getTenantId,dto.getTenantId())
                .remove();

        // 清除财险和增值服务台账数据
        iPdInsuranceLedgerService.lambdaUpdate()
                .between(PdInsuranceLedger::getOrderDate, monthDataStartDate, monthDataEndDate)
                .eq(PdInsuranceLedger::getTenantId,dto.getTenantId())
                .remove();
        iPdAddedLedgerService.lambdaUpdate()
                .between(PdAddedLedger::getOrderDate, monthDataStartDate, monthDataEndDate)
                .eq(PdAddedLedger::getTenantId,dto.getTenantId())
                .remove();

        // 使用 deleteByTypeAndDateRange 方法删除车险台账及相关数据
        DeleteByTypeDTO deleteDto = new DeleteByTypeDTO();
        deleteDto.setType(0); // 车险类型
        deleteDto.setTenantIds(Collections.singletonList(dto.getTenantId().toString())); // 单个租户ID转为列表
        deleteDto.setStartDate(dto.getMonthDataStart());
        deleteDto.setEndDate(dto.getMonthDataEnd());

        // 调用服务方法执行删除
        iPdLedgerService.deleteByTypeAndDateRange(deleteDto);

        // 清除其他相关数据（如果 deleteByTypeAndDateRange 方法没有处理）
        LocalDateTime startOfDay = dto.getMonthDataStart().atStartOfDay();  // 转为当天 0 点（LocalDateTime）
        Date startDate = java.sql.Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        LocalDateTime endOfDay = dto.getMonthDataEnd().atTime(23, 59, 59);  // 转为当天 23:59:59（LocalDateTime）
        Date endDate = java.sql.Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        iPdCasualtyInfoService.lambdaUpdate()
                .between(PdCasualtyInfo::getCreateTime, startDate, endDate)
                .eq(PdCasualtyInfo::getTenantId,dto.getTenantId())
                .remove();
        iPdAddedService.lambdaUpdate()
                .between(PdAdded::getCreateTime, startDate, endDate)
                .eq(PdAdded::getTenantId,dto.getTenantId())
                .remove();
        // 先删除指定租户和日期范围内的链接快照数据
        pdLinkRecodeService.deleteByTenantAndDateRange(dto.getTenantId(), startDate, endDate);

        // 调用链接指标快照补全方法
        pdLinkRecodeService.batchCompleteRecode(dto.getTenantId(), startDate, endDate);

        String configJson = dto.getConfigJson();
        DailyConfigContent configContent = objectMapper.readValue(configJson, DailyConfigContent.class);
        Map<String, LocalDateTime> resultMap = RandDayRangUtil.generateRandomTimes(dto.getMonthDataStart(), dto.getMonthDataEnd(), dto.getClickNum(), dto.getClickNum());
        Map<String, LocalDateTime> financeLedgerMap = Map.of();
        Map<String, LocalDateTime> chatUserTimeMapFinance= Map.of();
        Map<String, LocalDateTime> carLedgerMap= Map.of();

        Map<String, LocalDateTime> chatUserTimeMapCar= Map.of();
        Map<String, LocalDateTime> valueAddedLedgerMap= Map.of();
        Map<String, LocalDateTime> chatUserTimeMapValue= Map.of();
        if (!Objects.isNull( configContent.getFinanceLedger())) {
            //财险
            financeLedgerMap = RandDayNumUtil.pickByRandomRate(resultMap, configContent.getFinanceLedger().getLedgerStart(), configContent.getFinanceLedger().getLedgerEnd());
            chatUserTimeMapFinance = RandDayNumUtil.pickByRandomRate(financeLedgerMap, configContent.getFinanceLedger().getChatUserStart(), configContent.getFinanceLedger().getChatUserEnd());
        }
        if (!Objects.isNull( configContent.getCarLedger())) {
        //车险
        carLedgerMap = RandDayNumUtil.pickByRandomRate( resultMap, configContent.getCarLedger().getLedgerStart(), configContent.getCarLedger().getLedgerEnd());
       chatUserTimeMapCar = RandDayNumUtil.pickByRandomRate( carLedgerMap, configContent.getCarLedger().getChatUserStart(), configContent.getCarLedger().getChatUserEnd());
        }
        if (!Objects.isNull( configContent.getValueAddedLedger())) {
            //增值服务
            valueAddedLedgerMap = RandDayNumUtil.pickByRandomRate(resultMap, configContent.getValueAddedLedger().getLedgerStart(), configContent.getValueAddedLedger().getLedgerEnd());
            chatUserTimeMapValue = RandDayNumUtil.pickByRandomRate(valueAddedLedgerMap, configContent.getValueAddedLedger().getChatUserStart(), configContent.getValueAddedLedger().getChatUserEnd());
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //合作公司点击报表
        Map<String,ClickReport> clickReportMap = operateClickReport(dto,sdf,resultMap);
        Map<String,ClickReportHourly> clickReportHourMap = operateClickReportHour(dto,sdf,resultMap);
        List<PdCasualtyInfo> pdCasualtyInfoList = new ArrayList<>();
        List<PdCarInfo> carInfoList = new ArrayList<>();
        List<PdAdded> pdAddedList = new ArrayList<>();


        Integer tenantId = dto.getTenantId();

        List<PdLinkInfo> pdLinkInfoList = iPdLinkInfoService.lambdaQuery()
                .eq(PdLinkInfo::getTenantId, dto.getTenantId())
                .list();
        Map<Integer, Map<Integer, PdLinkInfo>> pdLinkInfoMapByTenantId = pdLinkInfoList.stream()
                .collect(Collectors.groupingBy(
                        PdLinkInfo::getTenantId, // 一级分组：tenantId
                        Collectors.toMap(
                                PdLinkInfo::getLinkType, // 二级分组：linkType
                                pd -> pd                 // 值：PdLinkInfo 对象
                        )
                ));
        // 随机生成3位数字
        Random random = new Random();
        //按照表格文件，参合台账
        saveByFileLedgers(files, financeLedgerMap, chatUserTimeMapFinance, pdCasualtyInfoList, configContent, pdLinkInfoMapByTenantId, carLedgerMap, chatUserTimeMapCar, carInfoList, random, valueAddedLedgerMap, chatUserTimeMapValue, pdAddedList);


        if (!CollectionUtils.isEmpty(pdCasualtyInfoList)){
            iPdCasualtyInfoService.saveBatch(pdCasualtyInfoList);
            updateChatCasualty( tenantId, pdCasualtyInfoList);
        }
        if (!CollectionUtils.isEmpty(pdAddedList)){
            iPdAddedService.saveBatch(pdAddedList);
            updateChatPdAdded( tenantId, pdAddedList);
        }
        if (!CollectionUtils.isEmpty(carInfoList)){

            iPdCarInfoService.saveBatch(carInfoList);
            updateChatCarInfo(tenantId, carInfoList);
        }
        if (!clickReportMap.isEmpty()){
            clickReportService.saveBatch(new ArrayList<>(clickReportMap.values()));
        }
        if (!clickReportHourMap.isEmpty()){
            clickReportHourlyService.saveBatch(new ArrayList<>(clickReportHourMap.values()));
        }
        //生成剩余台账
        autoCreateLedger(getPreList(financeLedgerMap, carLedgerMap, valueAddedLedgerMap,
                configContent, dto.getTenantId(),chatUserTimeMapFinance,chatUserTimeMapCar,chatUserTimeMapValue), dto.getTenantId());
    }

    private void saveByFileLedgers(List<MultipartFile> files, Map<String, LocalDateTime> financeLedgerMap, Map<String, LocalDateTime> chatUserTimeMapFinance, List<PdCasualtyInfo> pdCasualtyInfoList, DailyConfigContent configContent, Map<Integer, Map<Integer, PdLinkInfo>> pdLinkInfoMapByTenantId, Map<String, LocalDateTime> carLedgerMap, Map<String, LocalDateTime> chatUserTimeMapCar, List<PdCarInfo> carInfoList, Random random, Map<String, LocalDateTime> valueAddedLedgerMap, Map<String, LocalDateTime> chatUserTimeMapValue, List<PdAdded> pdAddedList) {
        if (!CollectionUtils.isEmpty(files)){
            for (MultipartFile file : files) {
                String filename = file.getOriginalFilename();
                if (filename == null) continue;

                if (filename.contains("财险")) {
                    List<PdInsuranceLedgerDTO> dtoList = ExcelUtils.read(file, PdInsuranceLedgerDTO.class);
                    for (PdInsuranceLedgerDTO ledgerDTO : dtoList){
                        if (financeLedgerMap.isEmpty()){
                            throw new JeecgBootException("台账区间配置异常");
                        }
                        Map.Entry<String, LocalDateTime> entry = pickAndRemoveRandomEntry(financeLedgerMap);
                        // 转换：只保留日期部分（不含时间）
                        ledgerDTO.setOrderDate(java.sql.Date.valueOf(entry.getValue().toLocalDate()));
                        if (chatUserTimeMapFinance.containsKey(entry.getKey())){
                            ledgerDTO.setHasChatUser(1);
                        }
                    }

                    List<PdInsuranceLedger> list = dtoList
                            .stream()
                            .map(this::convertToInsuranceEntity)
                            .distinct()
                            .collect(Collectors.toList());
                    for (PdInsuranceLedger pdInsuranceLedger : list){
                        pdCasualtyInfoList.add(createPdCasualtyInfo( pdInsuranceLedger, configContent.getRandomCity(), pdLinkInfoMapByTenantId.get(pdInsuranceLedger.getTenantId())));
                    }
                    iPdInsuranceLedgerService.saveBatch(list);

                } else if (filename.contains("车险")) {
                    List<PdLedgerDTO> dtoList = ExcelUtils.read(file, PdLedgerDTO.class);
                    for (PdLedgerDTO ledgerDTO : dtoList){
                        if (carLedgerMap.isEmpty()){
                            throw new JeecgBootException("台账区间配置异常");
                        }
                        Map.Entry<String, LocalDateTime> entry = pickAndRemoveRandomEntry(carLedgerMap);
                        ledgerDTO.setSignDate(entry.getValue().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        ledgerDTO.setSignDateTime(java.sql.Date.from(entry.getValue().atZone(ZoneId.systemDefault()).toInstant()));
                        if (chatUserTimeMapCar.containsKey(entry.getKey())){
                            ledgerDTO.setHasChatUser(1);
                        }
                    }

                    List<PdLedger> list = dtoList
                            .stream()
                            .map(this::convertToVehicleEntity)
                            .distinct()
                            .collect(Collectors.toList());
                    for (PdLedger pdLedger : list){
                        carInfoList.add(createCarInfo(pdLedger, random, new PdGuestUsers()));
                    }
                    iPdLedgerService.saveBatch(list);
                } else if (filename.contains("增值服务")) {
                    List<PdAddedLedgerDTO> dtoList = ExcelUtils.read(file, PdAddedLedgerDTO.class);
                    for (PdAddedLedgerDTO ledgerDTO : dtoList){
                        if (valueAddedLedgerMap.isEmpty()){
                            throw new JeecgBootException("台账区间配置异常");
                        }
                        Map.Entry<String, LocalDateTime> entry = pickAndRemoveRandomEntry(valueAddedLedgerMap);
                        // 转换：只保留日期部分（不含时间）
                        ledgerDTO.setOrderDate(java.sql.Date.valueOf(entry.getValue().toLocalDate()));
                        if (chatUserTimeMapValue.containsKey(entry.getKey())){
                            ledgerDTO.setHasChatUser(1);
                        }
                    }
                    List<PdAddedLedger> list = dtoList
                            .stream()
                            .map(this::convertToAddedEntity)
                            .distinct()
                            .collect(Collectors.toList());
                    for (PdAddedLedger pdAddedLedger : list){
                        pdAddedList.add(createPdAdded( pdAddedLedger, configContent.getRandomCity(), pdLinkInfoMapByTenantId.get(pdAddedLedger.getTenantId()), null));
                    }
                    iPdAddedLedgerService.saveBatch(list);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadMultipleFiles(MultipartFile file) {
        try {
            // 1. 读取Excel文件，转换为DailyConfigExcelDTO列表
            InputStream inputStream = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);  // 跳过前两行（标题行）
            params.setHeadRows(1);   // 第一行作为头行
            params.setNeedSave(true);

            // 读取Excel内容为List<DailyConfigExcelDTO>类型
            List<DailyConfigExcelDTO> configList = ExcelImportUtil.importExcel(inputStream, DailyConfigExcelDTO.class, params);
            configList.forEach(config -> {
                if (config.getMonthDataStart() == null){
                    throw new JeecgBootException("日期开始时间未填写,请确认是否为批量补全文件");
                }
            });

            if (configList.isEmpty()) {
                throw new IOException("导入的Excel文件没有数据");
            }

            // 2. 将配置信息转换为JSON并存入Redis的List结构中
            String redisKey = "tenant_batch_process:configs";
            // 清空之前的配置（如果有）
            redisTemplate.delete(redisKey);

            // 将每个配置转换为DailyConfigDto并存入Redis
            for (DailyConfigExcelDTO dto : configList) {
                // 创建DailyConfigDto对象
                DailyConfigDto dailyConfigDto = new DailyConfigDto();
                dailyConfigDto.setTenantId(dto.getTenantId());

                // 设置日期范围（使用我们的自定义转换方法）
                dailyConfigDto.setMonthDataStart(dto.getMonthDataStart());
                dailyConfigDto.setMonthDataEnd(dto.getMonthDataEnd());

                // 设置点击数
                dailyConfigDto.setClickNum(dto.getClickNum());

                // 构建配置JSON
                DailyConfigContent configContent = new DailyConfigContent();

                // 车险配置
                DailyConfigContent.Range carLedger = new DailyConfigContent.Range();
                carLedger.setLedgerStart(dto.getCarInsuranceStart());
                carLedger.setLedgerEnd(dto.getCarInsuranceEnd());
                carLedger.setChatUserStart(dto.getChatStart());
                carLedger.setChatUserEnd(dto.getChatEnd());
                configContent.setCarLedger(carLedger);

                // 财险配置
                DailyConfigContent.Range financeLedger = new DailyConfigContent.Range();
                financeLedger.setLedgerStart(dto.getPropertyInsuranceStart());
                financeLedger.setLedgerEnd(dto.getPropertyInsuranceEnd());
                financeLedger.setChatUserStart(dto.getPropertyChatStart());
                financeLedger.setChatUserEnd(dto.getPropertyChatEnd());
                configContent.setFinanceLedger(financeLedger);

                // 增值服务配置
                DailyConfigContent.Range valueAddedLedger = new DailyConfigContent.Range();
                valueAddedLedger.setLedgerStart(dto.getValueServiceStart());
                valueAddedLedger.setLedgerEnd(dto.getValueServiceEnd());
                valueAddedLedger.setChatUserStart(dto.getValueChatStart());
                valueAddedLedger.setChatUserEnd(dto.getValueChatEnd());
                configContent.setValueAddedLedger(valueAddedLedger);

                // 城市配置
                if (dto.getCities() != null && !dto.getCities().isEmpty()) {
                    // 将逗号分隔的城市字符串转换为List<String>类型，存储城市编码
                    List<String> cityList = new ArrayList<>();

                    // 按逗号分割城市名称
                    String[] cityNames = dto.getCities().split(",");

                    // 遍历每个城市名称，查询对应的城市编码
                    for (String cityName : cityNames) {
                        // 去除可能的空格
                        cityName = cityName.trim();
                        if (!cityName.isEmpty()) {
                            // 根据城市名称模糊查询城市信息
                            List<EaRegion> regions = eaRegionService.findByCityNameLike(cityName);

                            // 如果找到匹配的城市，添加其编码到列表中
                            if (regions != null && !regions.isEmpty()) {
                                // 取第一个匹配的城市编码
                                String cityCode = regions.get(0).getCode();
                                cityList.add(cityCode);
                                log.info("城市 [{}] 匹配到编码: {}", cityName, cityCode);
                            } else {
                                log.warn("城市 [{}] 未找到匹配的编码", cityName);
                            }
                        }
                    }

                    // 设置城市编码列表
                    configContent.setCityList(cityList);
                    log.info("处理城市列表完成，共 {} 个城市编码", cityList.size());
                }

                // 将配置内容转为JSON
                dailyConfigDto.setConfigJson(objectMapper.writeValueAsString(configContent));

                // 将DailyConfigDto对象转为JSON字符串并存入Redis
                String configJson = objectMapper.writeValueAsString(dailyConfigDto);
                redisTemplate.opsForList().rightPush(redisKey, configJson);
            }

            // 记录初始任务总数
            Long totalTasks = redisTemplate.opsForList().size(redisKey);
            redisTemplate.opsForValue().set("tenant_batch_process:total_tasks", totalTasks.toString());
            log.info("批量处理任务初始化: 总任务数={}", totalTasks);

            // 3. 启动异步处理任务
            processTenantConfigsAsync();

            log.info("成功将{}个租户配置加入处理队列，开始异步处理", configList.size());
        } catch (Exception e) {
            log.error("处理批量补全文件失败", e);
            throw new JeecgBootException("处理批量补全文件失败: " + e.getMessage());
        }
    }

    /**
     * 异步处理租户配置
     * 从Redis队列中逐个取出租户配置并处理
     */
    @Override
    public void processTenantConfigsAsync() {
        // 创建并启动一个新线程来处理队列
        Thread processThread = new Thread(() -> {
            String redisKey = "tenant_batch_process:configs";
            String processingKey = "tenant_batch_process:processing";
            String currentTenantKey = "tenant_batch_process:current_tenant";
            String completedTenantsKey = "tenant_batch_process:completed_tenants";

            try {
                // 设置处理状态为进行中
                redisTemplate.opsForValue().set(processingKey, "1");

                // 清空已完成租户列表
                redisTemplate.delete(completedTenantsKey);

                // 设置Redis过期时间到明天凌晨1点
                Calendar tomorrowEarly = Calendar.getInstance();
                tomorrowEarly.add(Calendar.DAY_OF_MONTH, 1);
                tomorrowEarly.set(Calendar.HOUR_OF_DAY, 1);
                tomorrowEarly.set(Calendar.MINUTE, 0);
                tomorrowEarly.set(Calendar.SECOND, 0);

                // 计算从现在到明天凌晨1点的秒数
                long secondsUntilTomorrowEnd = (tomorrowEarly.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                // 设置Redis过期时间
                redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(redisKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire("tenant_batch_process:total_tasks", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(currentTenantKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(completedTenantsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);


                while (true) {
                    // 从队列左侧弹出一个配置（先进先出）
                    String configJson = (String) redisTemplate.opsForList().leftPop(redisKey);

                    // 如果队列为空，处理完成
                    if (configJson == null) {
                        // 清除当前处理的租户信息
                        redisTemplate.delete(currentTenantKey);
                        break;
                    }

                    try {
                        // 将JSON直接转换回DailyConfigDto对象
                        DailyConfigDto dailyConfigDto = objectMapper.readValue(configJson, DailyConfigDto.class);
                        Integer tenantId = dailyConfigDto.getTenantId();

                        // 获取租户名称
                        String tenantName = "未知租户";
                        try {
                            // 查询租户信息
                            SysTenant tenant = sysTenantService.getById(tenantId);
                            if (tenant != null) {
                                tenantName = tenant.getName();
                            }
                        } catch (Exception e) {
                            log.error("获取租户名称失败", e);
                        }

                        // 记录开始时间
                        long startTime = System.currentTimeMillis();

                        // 获取点击数
                        int clickCount = 0;
                        try {
                            // 从配置中获取点击数
                            DailyConfigContent configContent = objectMapper.readValue(dailyConfigDto.getConfigJson(), DailyConfigContent.class);
                            if (configContent != null) {
                                // 获取总点击数
                                clickCount = dailyConfigDto.getClickNum() != null ? dailyConfigDto.getClickNum() : 0;
                            }
                        } catch (Exception e) {
                            log.error("获取点击数失败", e);
                        }

                        // 记录当前处理的租户信息
                        Map<String, Object> currentTenantInfo = new HashMap<>();
                        currentTenantInfo.put("tenantId", tenantId);
                        currentTenantInfo.put("tenantName", tenantName);
                        currentTenantInfo.put("startTime", startTime);
                        currentTenantInfo.put("clickCount", clickCount); // 添加点击数

                        // 将当前租户信息存入Redis
                        redisTemplate.opsForValue().set(currentTenantKey, objectMapper.writeValueAsString(currentTenantInfo));


                        // 调用importLedgers方法处理单个租户
                        this.importLedgers(null, dailyConfigDto);

                        // 记录结束时间和耗时
                        long endTime = System.currentTimeMillis();
                        long duration = endTime - startTime;

                        // 记录已完成的租户信息
                        Map<String, Object> completedTenantInfo = new HashMap<>();
                        completedTenantInfo.put("tenantId", tenantId);
                        completedTenantInfo.put("tenantName", tenantName);
                        completedTenantInfo.put("startTime", startTime);
                        completedTenantInfo.put("endTime", endTime);
                        completedTenantInfo.put("duration", duration);

                        // 从当前租户信息中获取点击数
                        Object currentTenantObj = redisTemplate.opsForValue().get(currentTenantKey);
                        if (currentTenantObj != null) {
                            try {
                                Map<String, Object> currentTenant = objectMapper.readValue(currentTenantObj.toString(),
                                    new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});
                                if (currentTenant.containsKey("clickCount")) {
                                    completedTenantInfo.put("clickCount", currentTenant.get("clickCount"));
                                }
                            } catch (Exception e) {
                                log.error("获取当前租户点击数失败", e);
                                completedTenantInfo.put("clickCount", 0);
                            }
                        } else {
                            completedTenantInfo.put("clickCount", 0);
                        }

                        // 将已完成的租户信息添加到列表
                        redisTemplate.opsForList().rightPush(completedTenantsKey, objectMapper.writeValueAsString(completedTenantInfo));


                        // 添加适当的延迟，避免系统负载过高
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("处理租户配置失败: {}", configJson, e);
                        // 处理失败的配置放回队列末尾，以便稍后重试
                        redisTemplate.opsForList().rightPush(redisKey, configJson);
                        // 添加延迟，避免立即重试
                        Thread.sleep(5000);
                    }
                }

                // 设置处理状态为已完成
                redisTemplate.opsForValue().set(processingKey, "0");

                // 确保所有数据在任务完成后保留到明天凌晨1点
                Calendar tomorrowEarly2 = Calendar.getInstance();
                tomorrowEarly2.add(Calendar.DAY_OF_MONTH, 1);
                tomorrowEarly2.set(Calendar.HOUR_OF_DAY, 1);
                tomorrowEarly2.set(Calendar.MINUTE, 0);
                tomorrowEarly2.set(Calendar.SECOND, 0);

                // 计算从现在到明天凌晨1点的秒数
                long secondsUntilEnd = (tomorrowEarly2.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                // 设置Redis过期时间
                redisTemplate.expire(processingKey, secondsUntilEnd, TimeUnit.SECONDS);
                redisTemplate.expire("tenant_batch_process:total_tasks", secondsUntilEnd, TimeUnit.SECONDS);
                redisTemplate.expire(currentTenantKey, secondsUntilEnd, TimeUnit.SECONDS);
                redisTemplate.expire(completedTenantsKey, secondsUntilEnd, TimeUnit.SECONDS);

            } catch (Exception e) {
                log.error("租户配置批处理失败", e);
                // 设置处理状态为失败
                try {
                    redisTemplate.opsForValue().set(processingKey, "-1");
                } catch (Exception ex) {
                    log.error("设置处理状态失败", ex);
                }
            }
        });

        // 设置为守护线程，不阻止JVM退出
        processThread.setDaemon(true);
        processThread.setName("TenantBatchProcessor");
        processThread.start();
    }

    //操作合作公司点击报表
    public Map<String,ClickReport>  operateClickReport(DailyConfigDto dto,SimpleDateFormat sdf,Map<String, LocalDateTime> resultMap){
        Date monthDataStartDate = java.sql.Date.from(dto.getMonthDataStart().atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date monthDataEndDate = java.sql.Date.from(dto.getMonthDataEnd().atStartOfDay(ZoneId.systemDefault()).toInstant());
        //todo 合作公司点击报表
//        clickReportService.lambdaUpdate()
//                .between(ClickReport::getStatDate, java.sql.Date.from(dto.getMonthDataStart().atStartOfDay(ZoneId.systemDefault()).toInstant())
//                        , java.sql.Date.from(dto.getMonthDataEnd().atStartOfDay(ZoneId.systemDefault()).toInstant()))
//                .eq(ClickReport::getTenantId,dto.getTenantId()).remove();

        Map<String, ClickReport> clickReportMap = new HashMap<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(monthDataStartDate);

        // 结束时间
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(monthDataEndDate);

        while (!calendar.after(endCalendar)) {
            Date currentDate = calendar.getTime();
            // 格式化当前日期为字符串作为键
            String dateKey = sdf.format(currentDate);
            // 如果当天不存在，创建新的 ClickReport
            if (!clickReportMap.containsKey(dateKey)) {
                ClickReport newReport = new ClickReport();
                newReport.setStatDate(currentDate);
                // 这里可以设置其他默认值，比如 newReport.setXXX(默认值);
                newReport.setClickNum(0);
                newReport.setCompanyId(dto.getTenantId().toString());
                newReport.setTenantId(dto.getTenantId());
                clickReportMap.put(dateKey, newReport);
            }
            // 日期 +1天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (String key : resultMap.keySet()){
            ClickReport clickReport = clickReportMap.get(resultMap.get(key).format(formatter));
            clickReport.setClickNum(clickReport.getClickNum() == null ? 1 : clickReport.getClickNum() + 1);

        }
        return clickReportMap;
    }


    //操作合作公司点击报表
    public Map<String,ClickReportHourly>  operateClickReportHour(DailyConfigDto dto,SimpleDateFormat sdf,Map<String, LocalDateTime> resultMap){
        Date monthDataStartDate = java.sql.Date.from(dto.getMonthDataStart().atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date monthDataEndDate = java.sql.Date.from(dto.getMonthDataEnd().atStartOfDay(ZoneId.systemDefault()).toInstant());

        Map<String, ClickReportHourly> clickReportHourlyMap = new HashMap<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(monthDataStartDate);

        // 结束时间
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(monthDataEndDate);

        while (!calendar.after(endCalendar)) {
            // 格式化当前日期为字符串作为键
            for (int i = 0; i < 24; i++) {
                Date currentDate = calendar.getTime();
                String dateKey = sdf.format(currentDate)+ i;
                // 如果当天不存在，创建新的 ClickReport
                if (!clickReportHourlyMap.containsKey(dateKey)) {
                    ClickReportHourly newReport = new ClickReportHourly();
                    newReport.setStatDate(currentDate);
                    // 这里可以设置其他默认值，比如 newReport.setXXX(默认值);
                    newReport.setClickNum(0);
                    newReport.setHour(i);
                    newReport.setTenantId(dto.getTenantId());
                    clickReportHourlyMap.put(dateKey, newReport);
                }
            }
            // 日期 +1天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (String key : resultMap.keySet()){
            LocalDateTime ldt = resultMap.get(key);
            String dateKey = ldt.format(formatter) + ldt.getHour(); // 拼接日期+小时作为key
            ClickReportHourly clickReport = clickReportHourlyMap.get(dateKey);
            clickReport.setClickNum(clickReport.getClickNum() == null ? 1 : clickReport.getClickNum() + 1);

        }
        return clickReportHourlyMap;
    }


    private PdInsuranceLedger convertToInsuranceEntity(PdInsuranceLedgerDTO dto) {
        PdInsuranceLedger entity = new PdInsuranceLedger();
        BeanUtils.copyProperties(dto, entity);
        if (StringUtils.isBlank(entity.getPhone())){
            entity.setPhone(ProvinceIpGenerator.generateMaskedPhone());
        }
//        entity.setIsVied(0);
        return entity;
    }

    private PdLedger convertToVehicleEntity(PdLedgerDTO dto) {
        PdLedger entity = new PdLedger();
        BeanUtils.copyProperties(dto, entity);
        if (StringUtils.isBlank(entity.getInsuranceName())){
            entity.setInsuranceName(RandomInsuranceTypeGenerator.generateRandomInsuranceType());
        }
        if (StringUtils.isBlank(entity.getPhoneNumber())){
            entity.setPhoneNumber(ProvinceIpGenerator.generateMaskedPhone());
        }
        if (StringUtils.isBlank(entity.getBrandModel())){
            entity.setBrandModel(RandomCarBrandGenerator.generateRandomCarBrand());
        }
        if (StringUtils.isBlank(entity.getVin())){
            entity.setVin(RandomVinGenerator.generateRandomVin());
        }
        //todo
        entity.setChatStatus(1);
        entity.setIsDelete("0");
        entity.setChatStatus(0);
        return entity;
    }

    private PdAddedLedger convertToAddedEntity(PdAddedLedgerDTO dto) {
        PdAddedLedger entity = new PdAddedLedger();
        BeanUtils.copyProperties(dto, entity);
        if (StringUtils.isBlank(entity.getPhone())){
            entity.setPhone(ProvinceIpGenerator.generateMaskedPhone());
        }
//        entity.setIsVied(0);
        return entity;
    }

    public Map.Entry<String, LocalDateTime> pickAndRemoveRandomEntry(Map<String, LocalDateTime> map) {
        if (map == null || map.isEmpty()) return null;

        int randomIndex = new Random().nextInt(map.size());
        Iterator<Map.Entry<String, LocalDateTime>> iterator = map.entrySet().iterator();

        for (int i = 0; i < randomIndex; i++) {
            iterator.next();
        }

        Map.Entry<String, LocalDateTime> entry = iterator.next();
        iterator.remove(); // 从 map 中移除这个元素

        return entry;
    }

    public java.sql.Date getRandTime(Map<String, LocalDateTime> map){
        if (map.isEmpty()){
            throw new JeecgBootException("台账区间配置异常");
        }
        Map.Entry<String, LocalDateTime> entry = pickAndRemoveRandomEntry(map);
        // 转换：只保留日期部分（不含时间）
        return java.sql.Date.valueOf(entry.getValue().toLocalDate());
    }


    public List<ClickAutoPre> getPreList(Map<String, LocalDateTime> financeLedgerMap,Map<String, LocalDateTime> carLedgerMap,Map<String, LocalDateTime> valueAddedLedgerMap,
                                         DailyConfigContent configContent,Integer tenantId,Map<String, LocalDateTime> chatUserTimeMapFinance,
                                         Map<String, LocalDateTime> chatUserTimeMapCar,Map<String, LocalDateTime> chatUserTimeMapValue){
        List<ClickAutoPre> preList = new ArrayList<>();
        preList.addAll(setMapData( financeLedgerMap, configContent, tenantId, 1,chatUserTimeMapFinance));
        preList.addAll(setMapData( carLedgerMap, configContent, tenantId, 3,chatUserTimeMapCar));
        preList.addAll(setMapData( valueAddedLedgerMap, configContent, tenantId, 2,chatUserTimeMapValue));
        return preList;
    }

    public List<ClickAutoPre> setMapData(Map<String, LocalDateTime> map,DailyConfigContent configContent,Integer tenantId,Integer ledgerType,Map<String, LocalDateTime> chatUserMap){
        List<ClickAutoPre> list = new ArrayList<>();
        for (String key : map.keySet()){
            ClickAutoPre clickAutoPre = new ClickAutoPre()
                    .setAutoCreate(1)
                    .setClickTime(Timestamp.valueOf(map.get(key)))
                    .setCity(configContent.getRandomCity())
                    .setTenantId(tenantId)
                    .setLedgerType(ledgerType);
            if (chatUserMap.containsKey(key)){
                clickAutoPre.setHasChatUser(1);
            }
            list.add(clickAutoPre);
        }
        return list;
    }

    @Override
    public Map<String, Object> getBatchProcessProgress() {
        Map<String, Object> progressInfo = new HashMap<>();
        String redisKey = "tenant_batch_process:configs";
        String processingKey = "tenant_batch_process:processing";
        String currentTenantKey = "tenant_batch_process:current_tenant";
        String completedTenantsKey = "tenant_batch_process:completed_tenants";

        try {
            // 获取队列中剩余的任务数
            Long remainingTasks = redisTemplate.opsForList().size(redisKey);

            // 获取处理状态
            Object processingStatus = redisTemplate.opsForValue().get(processingKey);
            String status = "未知";
            if (processingStatus != null) {
                switch (processingStatus.toString()) {
                    case "0":
                        status = "已完成";
                        break;
                    case "1":
                        status = "处理中";
                        break;
                    case "-1":
                        status = "处理失败";
                        break;
                    default:
                        status = "未知状态: " + processingStatus;
                }
            } else {
                // 如果没有处理状态，但有已完成的租户，则认为是已完成状态
                List<Object> completedTenants = redisTemplate.opsForList().range(completedTenantsKey, 0, -1);
                if (completedTenants != null && !completedTenants.isEmpty()) {
                    status = "已完成";
                    // 重新设置处理状态，确保数据一致性
                    redisTemplate.opsForValue().set(processingKey, "0");

                    // 设置过期时间到明天凌晨1点
                    Calendar tomorrowEarly = Calendar.getInstance();
                    tomorrowEarly.add(Calendar.DAY_OF_MONTH, 1);
                    tomorrowEarly.set(Calendar.HOUR_OF_DAY, 1);
                    tomorrowEarly.set(Calendar.MINUTE, 0);
                    tomorrowEarly.set(Calendar.SECOND, 0);

                    // 计算从现在到明天凌晨1点的秒数
                    long secondsUntilEnd = (tomorrowEarly.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                    // 设置Redis过期时间
                    redisTemplate.expire(processingKey, secondsUntilEnd, TimeUnit.SECONDS);
                    redisTemplate.expire(completedTenantsKey, secondsUntilEnd, TimeUnit.SECONDS);
                    redisTemplate.expire("tenant_batch_process:total_tasks", secondsUntilEnd, TimeUnit.SECONDS);
                }
            }

            // 获取初始任务总数（如果没有记录，则使用当前剩余数量）
            Object totalTasksObj = redisTemplate.opsForValue().get("tenant_batch_process:total_tasks");
            Long totalTasks = totalTasksObj != null ? Long.parseLong(totalTasksObj.toString()) : (remainingTasks != null ? remainingTasks : 0L);

            // 计算已完成的任务数
            Long completedTasks = totalTasks - (remainingTasks != null ? remainingTasks : 0L);

            // 创建统一的租户列表
            List<Map<String, Object>> allTenants = new ArrayList<>();

            // 获取当前正在处理的租户信息
            Object currentTenantObj = redisTemplate.opsForValue().get(currentTenantKey);
            if (currentTenantObj != null && "处理中".equals(status)) {
                try {
                    Map<String, Object> currentTenant = objectMapper.readValue(currentTenantObj.toString(),
                        new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                    // 添加状态字段
                    currentTenant.put("status", "处理中");
                    // 确保有租户ID和名称
                    if (currentTenant.containsKey("tenantId") && currentTenant.containsKey("tenantName")) {
                        // 将当前处理的租户添加到列表的第一位
                        allTenants.add(currentTenant);
                    }
                } catch (Exception e) {
                    log.error("解析当前租户信息失败", e);
                }
            }

            // 获取已完成的租户列表
            List<Object> completedTenantsList = redisTemplate.opsForList().range(completedTenantsKey, 0, -1);
            if (completedTenantsList != null && !completedTenantsList.isEmpty()) {
                for (Object item : completedTenantsList) {
                    try {
                        Map<String, Object> tenantInfo = objectMapper.readValue(item.toString(),
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 添加状态字段
                        tenantInfo.put("status", "已完成");
                        allTenants.add(tenantInfo);
                    } catch (Exception e) {
                        log.error("解析已完成租户信息失败", e);
                    }
                }
            }

            // 获取队列中等待处理的租户列表
            List<Object> pendingTenantsList = redisTemplate.opsForList().range(redisKey, 0, -1);
            if (pendingTenantsList != null && !pendingTenantsList.isEmpty()) {
                for (Object item : pendingTenantsList) {
                    try {
                        String configJson = item.toString();
                        DailyConfigDto dailyConfigDto = objectMapper.readValue(configJson, DailyConfigDto.class);

                        // 创建租户信息
                        Map<String, Object> tenantInfo = new HashMap<>();
                        tenantInfo.put("tenantId", dailyConfigDto.getTenantId());

                        // 尝试获取租户名称
                        String tenantName = "未知租户";
                        try {
                            // 这里可以根据实际情况从数据库或其他地方获取租户名称
                            // 如果没有可用的方法，就使用默认值
                            tenantName = "租户" + dailyConfigDto.getTenantId();
                        } catch (Exception e) {
                            log.error("获取租户名称失败", e);
                        }
                        tenantInfo.put("tenantName", tenantName);

                        // 获取点击数
                        int clickCount = 0;
                        try {
                            // 从配置中获取点击数
                            DailyConfigContent configContent = objectMapper.readValue(dailyConfigDto.getConfigJson(), DailyConfigContent.class);
                            if (configContent != null) {
                                // 获取总点击数
                                clickCount = dailyConfigDto.getClickNum() != null ? dailyConfigDto.getClickNum() : 0;
                            }
                        } catch (Exception e) {
                            log.error("获取点击数失败", e);
                        }
                        tenantInfo.put("clickCount", clickCount);

                        // 添加状态字段
                        tenantInfo.put("status", "排队中");

                        // 添加到租户列表
                        allTenants.add(tenantInfo);
                    } catch (Exception e) {
                        log.error("解析待处理租户信息失败", e);
                    }
                }
            }

            // 填充返回信息
            progressInfo.put("totalTasks", totalTasks);
            progressInfo.put("completedTasks", completedTasks);
            progressInfo.put("remainingTasks", remainingTasks != null ? remainingTasks : 0L);
            progressInfo.put("status", status);
            progressInfo.put("tenantList", allTenants);  // 统一的租户列表，包含当前处理和已完成的租户


        } catch (Exception e) {
            log.error("获取批量处理进度失败", e);
            progressInfo.put("error", "获取进度信息失败: " + e.getMessage());
        }

        return progressInfo;
    }

    /**
     * 获取点击数预生成批量处理任务的进度信息
     * @return 包含总任务数、已完成任务数、剩余任务数和处理状态的Map
     */
    public Map<String, Object> getClickPreBatchProcessProgress() {
        Map<String, Object> progressInfo = new HashMap<>();

        try {
            String redisKey = "click_pre_batch_process:configs";
            String processingKey = "click_pre_batch_process:processing";
            String currentConfigKey = "click_pre_batch_process:current_config";
            String completedConfigsKey = "click_pre_batch_process:completed_configs";

            // 获取队列中剩余的任务数
            Long remainingTasks = redisTemplate.opsForList().size(redisKey);

            // 获取处理状态
            Object processingStatus = redisTemplate.opsForValue().get(processingKey);
            String status = "未知";
            if (processingStatus != null) {
                switch (processingStatus.toString()) {
                    case "0":
                        status = "已完成";
                        break;
                    case "1":
                        status = "处理中";
                        break;
                    case "-1":
                        status = "处理失败";
                        break;
                    default:
                        status = "未知状态: " + processingStatus;
                }
            } else {
                // 如果没有处理状态，但有已完成的配置，则认为是已完成状态
                List<Object> completedConfigs = redisTemplate.opsForList().range(completedConfigsKey, 0, -1);
                if (completedConfigs != null && !completedConfigs.isEmpty()) {
                    status = "已完成";
                    // 重新设置处理状态，确保数据一致性
                    redisTemplate.opsForValue().set(processingKey, "0");

                    // 设置过期时间到明天结束
                    Calendar tomorrow = Calendar.getInstance();
                    tomorrow.add(Calendar.DAY_OF_MONTH, 1);
                    tomorrow.set(Calendar.HOUR_OF_DAY, 23);
                    tomorrow.set(Calendar.MINUTE, 59);
                    tomorrow.set(Calendar.SECOND, 59);

                    long secondsUntilTomorrowEnd = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                    // 设置Redis过期时间
                    redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                    redisTemplate.expire(completedConfigsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                    redisTemplate.expire("click_pre_batch_process:total_tasks", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                }
            }

            // 获取初始任务总数（如果没有记录，则使用当前剩余数量）
            Object totalTasksObj = redisTemplate.opsForValue().get("click_pre_batch_process:total_tasks");
            Long totalTasks = totalTasksObj != null ? Long.parseLong(totalTasksObj.toString()) : (remainingTasks != null ? remainingTasks : 0L);

            // 计算已完成的任务数
            Long completedTasks = totalTasks - (remainingTasks != null ? remainingTasks : 0L);

            // 创建统一的配置列表
            List<Map<String, Object>> allConfigs = new ArrayList<>();

            // 获取当前正在处理的配置信息
            Object currentConfigObj = redisTemplate.opsForValue().get(currentConfigKey);
            if (currentConfigObj != null && "处理中".equals(status)) {
                try {
                    Map<String, Object> currentConfig = objectMapper.readValue(currentConfigObj.toString(),
                        new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                    // 添加状态字段
                    currentConfig.put("status", "处理中");
                    // 确保有租户ID和名称
                    if (currentConfig.containsKey("tenantId") && currentConfig.containsKey("tenantName")) {
                        // 将当前处理的配置添加到列表的第一位
                        allConfigs.add(currentConfig);
                    }
                } catch (Exception e) {
                    log.error("解析当前配置信息失败", e);
                }
            }

            // 获取已完成的配置列表
            List<Object> completedConfigsList = redisTemplate.opsForList().range(completedConfigsKey, 0, -1);
            if (completedConfigsList != null && !completedConfigsList.isEmpty()) {
                for (Object item : completedConfigsList) {
                    try {
                        Map<String, Object> configInfo = objectMapper.readValue(item.toString(),
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 添加状态字段
                        configInfo.put("status", "已完成");
                        allConfigs.add(configInfo);
                    } catch (Exception e) {
                        log.error("解析已完成配置信息失败", e);
                    }
                }
            }

            // 获取队列中等待处理的配置列表
            List<Object> pendingConfigsList = redisTemplate.opsForList().range(redisKey, 0, -1);
            if (pendingConfigsList != null && !pendingConfigsList.isEmpty()) {
                for (Object item : pendingConfigsList) {
                    try {
                        String configJson = item.toString();
                        Map<String, Object> configInfo = objectMapper.readValue(configJson,
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 添加状态字段
                        configInfo.put("status", "待开始");

                        // 添加到配置列表
                        allConfigs.add(configInfo);
                    } catch (Exception e) {
                        log.error("解析待处理配置信息失败", e);
                    }
                }
            }

            // 填充返回信息
            progressInfo.put("totalTasks", totalTasks);
            progressInfo.put("completedTasks", completedTasks);
            progressInfo.put("remainingTasks", remainingTasks != null ? remainingTasks : 0L);
            progressInfo.put("status", status);
            progressInfo.put("configList", allConfigs);  // 统一的配置列表，包含当前处理和已完成的配置

        } catch (Exception e) {
            log.error("获取点击数预生成批量处理进度失败", e);
            progressInfo.put("error", "获取进度信息失败: " + e.getMessage());
        }

        return progressInfo;
    }
}
